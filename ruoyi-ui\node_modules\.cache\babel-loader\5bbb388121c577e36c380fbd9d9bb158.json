{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\project.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\project.js", "mtime": 1754037225573}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9yb290L0Rlc2t0b3AvXHU5ODc5XHU3NkVFXHU4QkIwXHU1RjU1XHVGRjA4XHU1NDM0XHU5Rjk5XHU5Rjk5XHVGRjA5L3RqdWhhaXRhbmdfbWluaWFwcC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVsUHJvamVjdCA9IGRlbFByb2plY3Q7CmV4cG9ydHMuZ2V0UHJvamVjdCA9IGdldFByb2plY3Q7CmV4cG9ydHMuZ2V0U3BvbnNvckltYWdlID0gZ2V0U3BvbnNvckltYWdlOwpleHBvcnRzLmxpc3RQcm9qZWN0ID0gbGlzdFByb2plY3Q7CmV4cG9ydHMudXBkYXRlU3BvbnNvckltYWdlID0gdXBkYXRlU3BvbnNvckltYWdlOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6aG555uu5oql5ZCN5YiX6KGoCmZ1bmN0aW9uIGxpc3RQcm9qZWN0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL3Byb2plY3QvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lpobnnm67miqXlkI3or6bnu4YKZnVuY3Rpb24gZ2V0UHJvamVjdChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0LycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g6I635Y+W6LWe5Yqp5ZWG5Zu+54mHCmZ1bmN0aW9uIGdldFNwb25zb3JJbWFnZSgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvcHJvamVjdC9jb25maWcvc3BvbnNvcicsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOabtOaWsOi1nuWKqeWVhuWbvueJhwpmdW5jdGlvbiB1cGRhdGVTcG9uc29ySW1hZ2UoaW1hZ2VVcmwpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvcHJvamVjdC9jb25maWcvc3BvbnNvcicsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogewogICAgICBzcG9uc29yVW5pdDogaW1hZ2VVcmwKICAgIH0KICB9KTsKfQoKLy8g5Yig6Zmk6aG555uu5oql5ZCNCmZ1bmN0aW9uIGRlbFByb2plY3QoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvcHJvamVjdC8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listProject", "query", "request", "url", "method", "params", "getProject", "id", "getSponsorImage", "updateSponsorImage", "imageUrl", "data", "sponsorUnit", "delProject"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/haitang/project.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询项目报名列表\r\nexport function listProject(query) {\r\n  return request({\r\n    url: '/miniapp/haitang/project/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询项目报名详细\r\nexport function getProject(id) {\r\n  return request({\r\n    url: '/miniapp/haitang/project/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取赞助商图片\r\nexport function getSponsorImage() {\r\n  return request({\r\n    url: '/miniapp/haitang/project/config/sponsor',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新赞助商图片\r\nexport function updateSponsorImage(imageUrl) {\r\n  return request({\r\n    url: '/miniapp/haitang/project/config/sponsor',\r\n    method: 'put',\r\n    data: { sponsorUnit: imageUrl }\r\n  })\r\n}\r\n\r\n// 删除项目报名\r\nexport function delProject(id) {\r\n  return request({\r\n    url: '/miniapp/haitang/project/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,EAAE;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAE;MAAEC,WAAW,EAAEF;IAAS;EAChC,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,UAAUA,CAACN,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,EAAE;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}