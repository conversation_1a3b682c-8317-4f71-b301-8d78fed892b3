{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImagePreview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImagePreview\\index.vue", "mtime": 1754030579760}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAiQC91dGlscy92YWxpZGF0ZSINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiSW1hZ2VQcmV2aWV3IiwNCiAgcHJvcHM6IHsNCiAgICBzcmM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiDQogICAgfSwNCiAgICB3aWR0aDogew0KICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwNCiAgICAgIGRlZmF1bHQ6ICIiDQogICAgfSwNCiAgICBoZWlnaHQ6IHsNCiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sDQogICAgICBkZWZhdWx0OiAiIg0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICByZWFsU3JjKCkgew0KICAgICAgaWYgKCF0aGlzLnNyYykgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGxldCByZWFsX3NyYyA9IHRoaXMuc3JjLnNwbGl0KCIsIilbMF0NCiAgICAgIGlmIChpc0V4dGVybmFsKHJlYWxfc3JjKSkgew0KICAgICAgICByZXR1cm4gcmVhbF9zcmMNCiAgICAgIH0NCiAgICAgIHJldHVybiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgcmVhbF9zcmMNCiAgICB9LA0KICAgIHJlYWxTcmNMaXN0KCkgew0KICAgICAgaWYgKCF0aGlzLnNyYykgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGxldCByZWFsX3NyY19saXN0ID0gdGhpcy5zcmMuc3BsaXQoIiwiKQ0KICAgICAgbGV0IHNyY0xpc3QgPSBbXQ0KICAgICAgcmVhbF9zcmNfbGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXNFeHRlcm5hbChpdGVtKSkgew0KICAgICAgICAgIHJldHVybiBzcmNMaXN0LnB1c2goaXRlbSkNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gc3JjTGlzdC5wdXNoKHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyBpdGVtKQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBzcmNMaXN0DQogICAgfSwNCiAgICByZWFsV2lkdGgoKSB7DQogICAgICByZXR1cm4gdHlwZW9mIHRoaXMud2lkdGggPT0gInN0cmluZyIgPyB0aGlzLndpZHRoIDogYCR7dGhpcy53aWR0aH1weGANCiAgICB9LA0KICAgIHJlYWxIZWlnaHQoKSB7DQogICAgICByZXR1cm4gdHlwZW9mIHRoaXMuaGVpZ2h0ID09ICJzdHJpbmciID8gdGhpcy5oZWlnaHQgOiBgJHt0aGlzLmhlaWdodH1weGANCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\r\n  <el-image\r\n    :src=\"`${realSrc}`\"\r\n    fit=\"cover\"\r\n    :style=\"`width:${realWidth};height:${realHeight};`\"\r\n    :preview-src-list=\"realSrcList\"\r\n  >\r\n    <div slot=\"error\" class=\"image-slot\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n    </div>\r\n  </el-image>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from \"@/utils/validate\"\r\n\r\nexport default {\r\n  name: \"ImagePreview\",\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    width: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    },\r\n    height: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    }\r\n  },\r\n  computed: {\r\n    realSrc() {\r\n      if (!this.src) {\r\n        return\r\n      }\r\n      let real_src = this.src.split(\",\")[0]\r\n      if (isExternal(real_src)) {\r\n        return real_src\r\n      }\r\n      return process.env.VUE_APP_BASE_API + real_src\r\n    },\r\n    realSrcList() {\r\n      if (!this.src) {\r\n        return\r\n      }\r\n      let real_src_list = this.src.split(\",\")\r\n      let srcList = []\r\n      real_src_list.forEach(item => {\r\n        if (isExternal(item)) {\r\n          return srcList.push(item)\r\n        }\r\n        return srcList.push(process.env.VUE_APP_BASE_API + item)\r\n      })\r\n      return srcList\r\n    },\r\n    realWidth() {\r\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`\r\n    },\r\n    realHeight() {\r\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-image {\r\n  border-radius: 5px;\r\n  background-color: #ebeef5;\r\n  box-shadow: 0 0 5px 1px #ccc;\r\n  ::v-deep .el-image__inner {\r\n    transition: all 0.3s;\r\n    cursor: pointer;\r\n    &:hover {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  ::v-deep .image-slot {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    color: #909399;\r\n    font-size: 30px;\r\n  }\r\n}\r\n</style>\r\n"]}]}