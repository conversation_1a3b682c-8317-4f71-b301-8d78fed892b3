# 小程序用户接口分离说明

## 问题描述

原来小程序和后台管理共用同一个用户列表查询接口 `/miniapp/user/list`，但是需求不同：
- **小程序端**：只需要获取未停用的用户（status = '0'）
- **后台管理**：需要获取所有用户，包括停用的用户

原有的 `selectUserListWithIndustry` 方法在 SQL 中硬编码了 `u.status = '0'` 条件，导致后台管理无法查看停用的用户。

## 解决方案

### 1. 接口分离

创建两个不同的接口来满足不同的需求：

#### 小程序端接口（保持不变）
- **接口路径**: `GET /miniapp/user/list`
- **用途**: 小程序端使用
- **查询范围**: 只查询未停用用户（status = '0'）
- **实现**: 使用原有的 `selectUserListWithIndustry` 方法

#### 后台管理接口（新增）
- **接口路径**: `GET /miniapp/user/admin/list`
- **用途**: 后台管理使用
- **查询范围**: 查询所有用户（包括停用的）
- **实现**: 使用新增的 `selectUserListWithIndustryAll` 方法

### 2. 代码修改

#### 后端修改

1. **Controller 层** (`MiniUserController.java`)
   - 保留原有的 `list()` 方法，添加 `user.setStatus("0")` 确保只查询未停用用户
   - 新增 `adminList()` 方法，使用新的 Service 方法

2. **Service 层** (`ISysUserService.java` 和 `SysUserServiceImpl.java`)
   - 新增 `selectUserListWithIndustryAll()` 方法

3. **Mapper 层** (`SysUserMapper.java`)
   - 新增 `selectUserListWithIndustryAll()` 方法声明

4. **SQL 映射** (`SysUserMapper.xml`)
   - 新增 `selectUserListWithIndustryAll` 查询，去掉硬编码的 `u.status = '0'` 条件
   - 保留原有的 `selectUserListWithIndustry` 查询不变

#### 前端修改

1. **API 层** (`user.js`)
   - 新增 `listMiniUserAdmin()` 方法调用新的后台管理接口

2. **页面层** (`index.vue`)
   - 修改后台管理页面使用新的 `listMiniUserAdmin()` API

### 3. 关键差异

| 方面 | 小程序端接口 | 后台管理接口 |
|------|-------------|-------------|
| 接口路径 | `/miniapp/user/list` | `/miniapp/user/admin/list` |
| SQL 方法 | `selectUserListWithIndustry` | `selectUserListWithIndustryAll` |
| 状态过滤 | 硬编码 `u.status = '0'` | 无硬编码状态过滤 |
| 查询结果 | 只有未停用用户 | 所有用户（包括停用的） |

### 4. 测试验证

创建了测试类 `MiniUserControllerTest.java` 来验证：
- 小程序端接口只返回未停用用户
- 后台管理接口返回所有用户

## 使用说明

### 小程序端
继续使用原有的接口，无需修改：
```javascript
listMiniUser(query) // 只返回未停用用户
```

### 后台管理
使用新的管理接口：
```javascript
listMiniUserAdmin(query) // 返回所有用户（包括停用的）
```

## 注意事项

1. **权限控制**: 两个接口都使用相同的权限控制 `@PreAuthorize("@ss.hasPermi('miniapp:user:list')")`
2. **向后兼容**: 原有的小程序端接口保持不变，确保向后兼容
3. **数据一致性**: 两个接口都查询相同的数据源，只是过滤条件不同
4. **性能考虑**: 新接口复用了原有的 SQL 逻辑，只是去掉了状态过滤，性能影响最小

## 部署说明

1. 先部署后端代码
2. 再部署前端代码
3. 测试两个接口是否正常工作
4. 验证小程序端仍然只能看到未停用用户
5. 验证后台管理可以看到所有用户（包括停用的）
