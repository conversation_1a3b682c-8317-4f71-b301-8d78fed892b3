package com.ruoyi.miniapp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目报名对象 mini_project_registration
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProjectRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 团队规模 */
    @Excel(name = "团队规模")
    private Integer teamSize;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 赛区 */
    @Excel(name = "赛区")
    private String competitionArea;

    /** 行业 */
    @Excel(name = "行业")
    private String industry;

    /** 是否天津大学校友 */
    @Excel(name = "是否天津大学校友", readConverterExp = "0=否,1=是")
    private Boolean isTjuAlumni;

    /** 项目描述 */
    @Excel(name = "项目描述")
    private String projectDescription;

    /** 是否有公司 */
    @Excel(name = "是否有公司", readConverterExp = "0=否,1=是")
    private Boolean hasCompany;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 去年营收 */
    @Excel(name = "去年营收")
    private BigDecimal lastYearRevenue;

    /** 项目估值 */
    @Excel(name = "项目估值")
    private BigDecimal projectValuation;

    /** 最新融资轮次 */
    @Excel(name = "最新融资轮次")
    private String latestFundingRound;

    /** 投资机构 */
    @Excel(name = "投资机构")
    private String investmentInstitution;

    /** 公司logo */
    @Excel(name = "公司logo")
    private String companyLogo;

    /** 项目商业计划书 */
    @Excel(name = "项目商业计划书")
    private String projectBp;

    /** 推荐人 */
    @Excel(name = "推荐人")
    private String recommender;

    /** 赞助单位图片URL */
    @Excel(name = "赞助单位图片URL")
    private String sponsorUnit;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String contactPhone;

    /** 联系人微信 */
    @Excel(name = "联系人微信")
    private String contactWechat;

    /** 联系人职位 */
    @Excel(name = "联系人职位")
    private String contactPosition;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setProjectName(String projectName)
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setTeamSize(Integer teamSize) 
    {
        this.teamSize = teamSize;
    }

    public Integer getTeamSize() 
    {
        return teamSize;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setCompetitionArea(String competitionArea) 
    {
        this.competitionArea = competitionArea;
    }

    public String getCompetitionArea() 
    {
        return competitionArea;
    }
    public void setIndustry(String industry) 
    {
        this.industry = industry;
    }

    public String getIndustry() 
    {
        return industry;
    }
    public void setIsTjuAlumni(Boolean isTjuAlumni) 
    {
        this.isTjuAlumni = isTjuAlumni;
    }

    public Boolean getIsTjuAlumni() 
    {
        return isTjuAlumni;
    }
    public void setProjectDescription(String projectDescription) 
    {
        this.projectDescription = projectDescription;
    }

    public String getProjectDescription() 
    {
        return projectDescription;
    }
    public void setHasCompany(Boolean hasCompany) 
    {
        this.hasCompany = hasCompany;
    }

    public Boolean getHasCompany() 
    {
        return hasCompany;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setLastYearRevenue(BigDecimal lastYearRevenue) 
    {
        this.lastYearRevenue = lastYearRevenue;
    }

    public BigDecimal getLastYearRevenue() 
    {
        return lastYearRevenue;
    }
    public void setProjectValuation(BigDecimal projectValuation) 
    {
        this.projectValuation = projectValuation;
    }

    public BigDecimal getProjectValuation() 
    {
        return projectValuation;
    }
    public void setLatestFundingRound(String latestFundingRound) 
    {
        this.latestFundingRound = latestFundingRound;
    }

    public String getLatestFundingRound() 
    {
        return latestFundingRound;
    }
    public void setInvestmentInstitution(String investmentInstitution) 
    {
        this.investmentInstitution = investmentInstitution;
    }

    public String getInvestmentInstitution() 
    {
        return investmentInstitution;
    }
    public void setCompanyLogo(String companyLogo) 
    {
        this.companyLogo = companyLogo;
    }

    public String getCompanyLogo() 
    {
        return companyLogo;
    }
    public void setProjectBp(String projectBp) 
    {
        this.projectBp = projectBp;
    }

    public String getProjectBp() 
    {
        return projectBp;
    }
    public void setRecommender(String recommender) 
    {
        this.recommender = recommender;
    }

    public String getRecommender() 
    {
        return recommender;
    }
    public void setSponsorUnit(String sponsorUnit) 
    {
        this.sponsorUnit = sponsorUnit;
    }

    public String getSponsorUnit() 
    {
        return sponsorUnit;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactWechat(String contactWechat) 
    {
        this.contactWechat = contactWechat;
    }

    public String getContactWechat() 
    {
        return contactWechat;
    }
    public void setContactPosition(String contactPosition) 
    {
        this.contactPosition = contactPosition;
    }

    public String getContactPosition() 
    {
        return contactPosition;
    }
    public void setRegistrationTime(Date registrationTime) 
    {
        this.registrationTime = registrationTime;
    }

    public Date getRegistrationTime()
    {
        return registrationTime;
    }

    public void setCreatedAt(Date createdAt)
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("projectName", getProjectName())
            .append("teamSize", getTeamSize())
            .append("city", getCity())
            .append("competitionArea", getCompetitionArea())
            .append("industry", getIndustry())
            .append("isTjuAlumni", getIsTjuAlumni())
            .append("projectDescription", getProjectDescription())
            .append("hasCompany", getHasCompany())
            .append("companyName", getCompanyName())
            .append("lastYearRevenue", getLastYearRevenue())
            .append("projectValuation", getProjectValuation())
            .append("latestFundingRound", getLatestFundingRound())
            .append("investmentInstitution", getInvestmentInstitution())
            .append("companyLogo", getCompanyLogo())
            .append("projectBp", getProjectBp())
            .append("recommender", getRecommender())
            .append("sponsorUnit", getSponsorUnit())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("contactWechat", getContactWechat())
            .append("contactPosition", getContactPosition())
            .append("registrationTime", getRegistrationTime())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
} 