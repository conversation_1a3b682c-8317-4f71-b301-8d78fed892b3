import request from '@/utils/request'

// 查询项目报名列表
export function listProject(query) {
  return request({
    url: '/miniapp/haitang/project/list',
    method: 'get',
    params: query
  })
}

// 查询项目报名详细
export function getProject(id) {
  return request({
    url: '/miniapp/haitang/project/' + id,
    method: 'get'
  })
}

// 获取赞助商图片
export function getSponsorImage() {
  return request({
    url: '/miniapp/haitang/project/config/sponsor',
    method: 'get'
  })
}

// 更新赞助商图片
export function updateSponsorImage(imageUrl) {
  return request({
    url: '/miniapp/haitang/project/config/sponsor',
    method: 'put',
    data: { sponsorUnit: imageUrl }
  })
}

// 删除项目报名
export function delProject(id) {
  return request({
    url: '/miniapp/haitang/project/' + id,
    method: 'delete'
  })
}
