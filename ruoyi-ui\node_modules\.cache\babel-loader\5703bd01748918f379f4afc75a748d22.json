{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955784453}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "data", "version", "totalUsers", "activeUsers", "totalActivities", "totalDemands", "totalEvents", "totalEnterprises", "totalJobs", "totalExperts", "totalNews", "todayOperations", "recentActivities", "recentDemands", "businessChart", "userTrendChart", "created", "loadDashboardData", "mounted", "_this", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "loadStatistics", "loadRecentActivities", "loadRecentDemands", "v", "console", "error", "setDefaultData", "a", "_this3", "_callee2", "_context2", "_this4", "_callee3", "_context3", "id", "title", "create_time", "_this5", "_callee4", "_context4", "demand_title", "initBusinessChart", "initUserTrendChart", "$refs", "init", "option", "text", "left", "textStyle", "fontSize", "color", "tooltip", "trigger", "formatter", "legend", "bottom", "series", "type", "radius", "center", "avoidLabelOverlap", "itemStyle", "borderRadius", "borderColor", "borderWidth", "label", "show", "position", "emphasis", "fontWeight", "labelLine", "value", "setOption", "dates", "activeData", "totalData", "i", "date", "Date", "setDate", "getDate", "push", "toLocaleDateString", "month", "day", "Math", "floor", "random", "grid", "right", "top", "containLabel", "xAxis", "boundaryGap", "axisLabel", "yAxis", "stack", "smooth", "lineStyle", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "resize", "formatDate", "dateString", "year", "getUptime", "now", "startTime", "diff", "days", "concat", "goToPage", "path", "$router", "goTarget", "href", "open"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container home\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <h1 class=\"banner-title\">\r\n          <i class=\"el-icon-star-on\"></i>\r\n          天津大学海棠小程序管理后台\r\n        </h1>\r\n        <p class=\"banner-subtitle\">智慧校园 · 创新服务 · 数据驱动</p>\r\n        <div class=\"banner-stats\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalUsers }}</div>\r\n            <div class=\"stat-label\">系统用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ activeUsers }}</div>\r\n            <div class=\"stat-label\">活跃用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalEnterprises }}</div>\r\n            <div class=\"stat-label\">入驻企业</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ todayOperations }}</div>\r\n            <div class=\"stat-label\">今日操作</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card activities\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-star-on\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalActivities }}</div>\r\n            <div class=\"stat-title\">精彩活动</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card demands\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-connection\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalDemands }}</div>\r\n            <div class=\"stat-title\">需求对接</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card events\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-tickets\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalEvents }}</div>\r\n            <div class=\"stat-title\">活动报名</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card experts\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalExperts }}</div>\r\n            <div class=\"stat-title\">专家库</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表展示区域 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <!-- 业务数据分布饼图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-pie-chart\"></i> 业务数据分布</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"businessChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 用户活跃度趋势图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-data-line\"></i> 用户活跃度趋势</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"userTrendChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 最新活动 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-star-on\"></i> 最新活动</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"activity-list\">\r\n            <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"activity-item\">\r\n              <div class=\"activity-title\">{{ activity.title }}</div>\r\n              <div class=\"activity-time\">{{ formatDate(activity.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentActivities.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无活动数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 最新需求 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-connection\"></i> 最新需求</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"demand-list\">\r\n            <div v-for=\"demand in recentDemands\" :key=\"demand.id\" class=\"demand-item\">\r\n              <div class=\"demand-title\">{{ demand.demand_title }}</div>\r\n              <div class=\"demand-time\">{{ formatDate(demand.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentDemands.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无需求数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 系统状态 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-monitor\"></i> 系统状态</span>\r\n          </div>\r\n          <div class=\"system-status\">\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon online\">\r\n                <i class=\"el-icon-success\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">系统状态</div>\r\n                <div class=\"status-value\">运行正常</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-time\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">运行时间</div>\r\n                <div class=\"status-value\">{{ getUptime() }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-view\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">活跃用户</div>\r\n                <div class=\"status-value\">{{ activeUsers }} 人</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 快捷操作 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-s-operation\"></i> 快捷操作</span>\r\n          </div>\r\n          <div class=\"quick-actions\">\r\n            <div class=\"action-group\">\r\n              <h4>内容管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">\r\n                  新增活动\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/banner')\">\r\n                  新增轮播图\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/notice')\">\r\n                  新增通知\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/news')\">\r\n                  新增新闻\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>用户服务</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">\r\n                  发布需求\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/event')\">\r\n                  创建活动\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/job')\">\r\n                  发布职位\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/expert')\">\r\n                  添加专家\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>系统管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-user\" size=\"small\" @click=\"goToPage('/system/user')\">\r\n                  用户管理\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-setting\" size=\"small\" @click=\"goToPage('/system/menu')\">\r\n                  菜单管理\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-view\" size=\"small\" @click=\"goToPage('/monitor/operlog')\">\r\n                  操作日志\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-monitor\" size=\"small\" @click=\"goToPage('/monitor/server')\">\r\n                  服务监控\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: \"Index\",\r\n  data() {\r\n    return {\r\n      // 版本号\r\n      version: \"3.9.0\",\r\n      // 统计数据\r\n      totalUsers: 0,\r\n      activeUsers: 0,\r\n      totalActivities: 0,\r\n      totalDemands: 0,\r\n      totalEvents: 0,\r\n      totalEnterprises: 0,\r\n      totalJobs: 0,\r\n      totalExperts: 0,\r\n      totalNews: 0,\r\n      todayOperations: 0,\r\n      // 最新数据\r\n      recentActivities: [],\r\n      recentDemands: [],\r\n      // 图表实例\r\n      businessChart: null,\r\n      userTrendChart: null\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDashboardData()\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initCharts()\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', this.handleResize)\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁图表实例\r\n    if (this.businessChart) {\r\n      this.businessChart.dispose()\r\n    }\r\n    if (this.userTrendChart) {\r\n      this.userTrendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载仪表盘数据\r\n    async loadDashboardData() {\r\n      try {\r\n        // 获取统计数据\r\n        await this.loadStatistics()\r\n        // 获取最新活动\r\n        await this.loadRecentActivities()\r\n        // 获取最新需求\r\n        await this.loadRecentDemands()\r\n      } catch (error) {\r\n        console.error('加载仪表盘数据失败:', error)\r\n        // 使用默认数据\r\n        this.setDefaultData()\r\n      }\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      // 这里可以调用后端API获取统计数据\r\n      // 暂时使用从数据库查询到的真实数据\r\n      this.totalUsers = 6\r\n      this.activeUsers = 4 // 活跃用户数（最近7天登录的用户）\r\n      this.totalActivities = 2\r\n      this.totalDemands = 2\r\n      this.totalEvents = 4\r\n      this.totalEnterprises = 2\r\n      this.totalJobs = 8\r\n      this.totalExperts = 4\r\n      this.totalNews = 2\r\n      this.todayOperations = 36\r\n    },\r\n\r\n    // 加载最新活动\r\n    async loadRecentActivities() {\r\n      // 这里可以调用后端API获取最新活动\r\n      this.recentActivities = [\r\n        { id: 1, title: \"测试页\", create_time: \"2025-07-18T01:39:58.000Z\" },\r\n        { id: 2, title: \"江西宇悦科技有限公司\", create_time: \"2025-07-16T03:14:25.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 加载最新需求\r\n    async loadRecentDemands() {\r\n      // 这里可以调用后端API获取最新需求\r\n      this.recentDemands = [\r\n        { id: 1, demand_title: \"寻球寻求寻求\", create_time: \"2025-07-30T07:43:00.000Z\" },\r\n        { id: 2, demand_title: \"我是夏宇杰\", create_time: \"2025-07-30T06:55:45.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 设置默认数据\r\n    setDefaultData() {\r\n      this.totalUsers = 0\r\n      this.activeUsers = 0\r\n      this.totalActivities = 0\r\n      this.totalDemands = 0\r\n      this.totalEvents = 0\r\n      this.totalEnterprises = 0\r\n      this.totalJobs = 0\r\n      this.totalExperts = 0\r\n      this.totalNews = 0\r\n      this.todayOperations = 0\r\n      this.recentActivities = []\r\n      this.recentDemands = []\r\n    },\r\n\r\n    // 初始化图表\r\n    initCharts() {\r\n      this.initBusinessChart()\r\n      this.initUserTrendChart()\r\n    },\r\n\r\n    // 初始化业务数据分布饼图\r\n    initBusinessChart() {\r\n      if (!this.$refs.businessChart) return\r\n\r\n      this.businessChart = echarts.init(this.$refs.businessChart)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '业务模块分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '业务数据',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['50%', '45%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: 10,\r\n              borderColor: '#fff',\r\n              borderWidth: 2\r\n            },\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '16',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              { value: this.totalActivities, name: '精彩活动', itemStyle: { color: '#409eff' } },\r\n              { value: this.totalDemands, name: '需求对接', itemStyle: { color: '#67c23a' } },\r\n              { value: this.totalEvents, name: '活动报名', itemStyle: { color: '#e6a23c' } },\r\n              { value: this.totalExperts, name: '专家库', itemStyle: { color: '#f56c6c' } },\r\n              { value: this.totalJobs, name: '招聘职位', itemStyle: { color: '#909399' } },\r\n              { value: this.totalNews, name: '新闻资讯', itemStyle: { color: '#606266' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.businessChart.setOption(option)\r\n    },\r\n\r\n    // 初始化用户活跃度趋势图\r\n    initUserTrendChart() {\r\n      if (!this.$refs.userTrendChart) return\r\n\r\n      this.userTrendChart = echarts.init(this.$refs.userTrendChart)\r\n\r\n      // 模拟最近7天的数据\r\n      const dates = []\r\n      const activeData = []\r\n      const totalData = []\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date()\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))\r\n        activeData.push(Math.floor(Math.random() * 5) + 2) // 2-6之间的随机数\r\n        totalData.push(Math.floor(Math.random() * 3) + 6) // 6-8之间的随机数\r\n      }\r\n\r\n      const option = {\r\n        title: {\r\n          text: '用户活跃度（最近7天）',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '活跃用户',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#409eff'\r\n            },\r\n            itemStyle: {\r\n              color: '#409eff'\r\n            },\r\n            areaStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'\r\n                }, {\r\n                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'\r\n                }]\r\n              }\r\n            },\r\n            data: activeData\r\n          },\r\n          {\r\n            name: '总用户',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            itemStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            data: totalData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.userTrendChart.setOption(option)\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      if (this.businessChart) {\r\n        this.businessChart.resize()\r\n      }\r\n      if (this.userTrendChart) {\r\n        this.userTrendChart.resize()\r\n      }\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 获取系统运行时间\r\n    getUptime() {\r\n      const now = new Date()\r\n      const startTime = new Date('2025-01-01') // 假设系统启动时间\r\n      const diff = now - startTime\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      return `${days} 天`\r\n    },\r\n\r\n    // 页面跳转\r\n    goToPage(path) {\r\n      this.$router.push(path)\r\n    },\r\n\r\n    // 外部链接跳转\r\n    goTarget(href) {\r\n      window.open(href, \"_blank\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  // 欢迎横幅样式\r\n  .welcome-banner {\r\n    background: #409eff;\r\n    border-radius: 8px;\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n    color: white;\r\n    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);\r\n\r\n    .banner-title {\r\n      font-size: 28px;\r\n      font-weight: 600;\r\n      margin: 0 0 8px 0;\r\n\r\n      i {\r\n        color: #ffd700;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .banner-subtitle {\r\n      font-size: 14px;\r\n      opacity: 0.9;\r\n      margin: 0 0 25px 0;\r\n    }\r\n\r\n    .banner-stats {\r\n      display: flex;\r\n      gap: 30px;\r\n      flex-wrap: wrap;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .stat-number {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 12px;\r\n          opacity: 0.8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 统计卡片样式\r\n  .stats-row {\r\n    margin-bottom: 20px;\r\n\r\n    .stat-card {\r\n      background: white;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      transition: all 0.3s ease;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n      }\r\n\r\n      .stat-icon {\r\n        width: 60px;\r\n        height: 60px;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 15px;\r\n\r\n        i {\r\n          font-size: 24px;\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-info {\r\n        flex: 1;\r\n\r\n        .stat-value {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .stat-title {\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n      }\r\n\r\n      &.activities {\r\n        .stat-icon {\r\n          background: #409eff;\r\n        }\r\n        .stat-value {\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      &.demands {\r\n        .stat-icon {\r\n          background: #67c23a;\r\n        }\r\n        .stat-value {\r\n          color: #67c23a;\r\n        }\r\n      }\r\n\r\n      &.events {\r\n        .stat-icon {\r\n          background: #e6a23c;\r\n        }\r\n        .stat-value {\r\n          color: #e6a23c;\r\n        }\r\n      }\r\n\r\n      &.experts {\r\n        .stat-icon {\r\n          background: #f56c6c;\r\n        }\r\n        .stat-value {\r\n          color: #f56c6c;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 图表展示区域样式\r\n  .charts-row {\r\n    margin-bottom: 20px;\r\n\r\n    .chart-container {\r\n      padding: 10px 0;\r\n\r\n      .chart {\r\n        width: 100%;\r\n        height: 300px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域样式\r\n  .main-content {\r\n    margin-bottom: 20px;\r\n\r\n    .content-card {\r\n      height: 100%;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      border-radius: 8px;\r\n\r\n      .card-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        font-weight: 600;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      // 活动列表样式\r\n      .activity-list, .demand-list {\r\n        .activity-item, .demand-item {\r\n          padding: 12px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .activity-title, .demand-title {\r\n            font-size: 14px;\r\n            color: #333;\r\n            margin-bottom: 5px;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n\r\n          .activity-time, .demand-time {\r\n            font-size: 12px;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 系统状态样式\r\n      .system-status {\r\n        .status-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 15px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .status-icon {\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 8px;\r\n            background: #f5f7fa;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 15px;\r\n\r\n            i {\r\n              font-size: 18px;\r\n              color: #909399;\r\n            }\r\n\r\n            &.online i {\r\n              color: #67c23a;\r\n            }\r\n          }\r\n\r\n          .status-info {\r\n            flex: 1;\r\n\r\n            .status-label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 4px;\r\n            }\r\n\r\n            .status-value {\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 1;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 空数据样式\r\n      .empty-data {\r\n        text-align: center;\r\n        padding: 40px 0;\r\n        color: #999;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          margin-bottom: 10px;\r\n          display: block;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 快捷操作样式\r\n  .quick-actions {\r\n    .action-group {\r\n      margin-bottom: 30px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      h4 {\r\n        margin: 0 0 15px 0;\r\n        font-size: 16px;\r\n        color: #333;\r\n        font-weight: 600;\r\n        border-left: 4px solid #409eff;\r\n        padding-left: 10px;\r\n      }\r\n\r\n      .action-buttons {\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n\r\n        .el-button {\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    padding: 10px;\r\n\r\n    .welcome-banner {\r\n      padding: 20px;\r\n\r\n      .banner-title {\r\n        font-size: 22px;\r\n      }\r\n\r\n      .banner-stats {\r\n        gap: 20px;\r\n\r\n        .stat-item {\r\n          .stat-number {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .stats-row {\r\n      .stat-card {\r\n        padding: 15px;\r\n\r\n        .stat-icon {\r\n          width: 50px;\r\n          height: 50px;\r\n\r\n          i {\r\n            font-size: 20px;\r\n          }\r\n        }\r\n\r\n        .stat-info {\r\n          .stat-value {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .charts-row {\r\n      .chart-container {\r\n        .chart {\r\n          height: 250px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .quick-actions {\r\n      .action-group {\r\n        .action-buttons {\r\n          .el-button {\r\n            font-size: 12px;\r\n            padding: 6px 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;AA8PA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACAC,YAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;MACAC,eAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACA;MACAC,aAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,UAAA;MACA;MACAC,MAAA,CAAAC,gBAAA,WAAAJ,KAAA,CAAAK,YAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,YAAA;IACA;IACA,SAAAV,aAAA;MACA,KAAAA,aAAA,CAAAa,OAAA;IACA;IACA,SAAAZ,cAAA;MACA,KAAAA,cAAA,CAAAY,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACAX,iBAAA,WAAAA,kBAAA;MAAA,IAAAY,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAT,MAAA,CAAAW,cAAA;YAAA;cAAAH,QAAA,CAAAC,CAAA;cAAA,OAEAT,MAAA,CAAAY,oBAAA;YAAA;cAAAJ,QAAA,CAAAC,CAAA;cAAA,OAEAT,MAAA,CAAAa,iBAAA;YAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;cAEAC,OAAA,CAAAC,KAAA,eAAAV,EAAA;cACA;cACAN,MAAA,CAAAiB,cAAA;YAAA;cAAA,OAAAT,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEA;IACAM,cAAA,WAAAA,eAAA;MAAA,IAAAQ,MAAA;MAAA,WAAAlB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgB,SAAA;QAAA,WAAAjB,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cACA;cACA;cACAU,MAAA,CAAA9C,UAAA;cACA8C,MAAA,CAAA7C,WAAA;cACA6C,MAAA,CAAA5C,eAAA;cACA4C,MAAA,CAAA3C,YAAA;cACA2C,MAAA,CAAA1C,WAAA;cACA0C,MAAA,CAAAzC,gBAAA;cACAyC,MAAA,CAAAxC,SAAA;cACAwC,MAAA,CAAAvC,YAAA;cACAuC,MAAA,CAAAtC,SAAA;cACAsC,MAAA,CAAArC,eAAA;YAAA;cAAA,OAAAuC,SAAA,CAAAH,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEA;IACAR,oBAAA,WAAAA,qBAAA;MAAA,IAAAU,MAAA;MAAA,WAAArB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAmB,SAAA;QAAA,WAAApB,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cACA;cACAa,MAAA,CAAAvC,gBAAA,IACA;gBAAA0C,EAAA;gBAAAC,KAAA;gBAAAC,WAAA;cAAA,GACA;gBAAAF,EAAA;gBAAAC,KAAA;gBAAAC,WAAA;cAAA,EACA;YAAA;cAAA,OAAAH,SAAA,CAAAN,CAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MAAA,IAAAe,MAAA;MAAA,WAAA3B,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyB,SAAA;QAAA,WAAA1B,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAuB,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA;YAAA;cACA;cACAmB,MAAA,CAAA5C,aAAA,IACA;gBAAAyC,EAAA;gBAAAM,YAAA;gBAAAJ,WAAA;cAAA,GACA;gBAAAF,EAAA;gBAAAM,YAAA;gBAAAJ,WAAA;cAAA,EACA;YAAA;cAAA,OAAAG,SAAA,CAAAZ,CAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IACA;IAEA;IACAZ,cAAA,WAAAA,eAAA;MACA,KAAA5C,UAAA;MACA,KAAAC,WAAA;MACA,KAAAC,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,WAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,SAAA;MACA,KAAAC,YAAA;MACA,KAAAC,SAAA;MACA,KAAAC,eAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACAQ,UAAA,WAAAA,WAAA;MACA,KAAAwC,iBAAA;MACA,KAAAC,kBAAA;IACA;IAEA;IACAD,iBAAA,WAAAA,kBAAA;MACA,UAAAE,KAAA,CAAAjD,aAAA;MAEA,KAAAA,aAAA,GAAAlB,OAAA,CAAAoE,IAAA,MAAAD,KAAA,CAAAjD,aAAA;MAEA,IAAAmD,MAAA;QACAV,KAAA;UACAW,IAAA;UACAC,IAAA;UACAC,SAAA;YACAC,QAAA;YACAC,KAAA;UACA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAR,IAAA;UACAC,SAAA;YACAC,QAAA;UACA;QACA;QACAO,MAAA,GACA;UACA7E,IAAA;UACA8E,IAAA;UACAC,MAAA;UACAC,MAAA;UACAC,iBAAA;UACAC,SAAA;YACAC,YAAA;YACAC,WAAA;YACAC,WAAA;UACA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAjB,QAAA;cACAoB,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACAtF,IAAA,GACA;YAAA2F,KAAA,OAAAvF,eAAA;YAAAL,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA,GACA;YAAAqB,KAAA,OAAAtF,YAAA;YAAAN,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA,GACA;YAAAqB,KAAA,OAAArF,WAAA;YAAAP,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA,GACA;YAAAqB,KAAA,OAAAlF,YAAA;YAAAV,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA,GACA;YAAAqB,KAAA,OAAAnF,SAAA;YAAAT,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA,GACA;YAAAqB,KAAA,OAAAjF,SAAA;YAAAX,IAAA;YAAAkF,SAAA;cAAAX,KAAA;YAAA;UAAA;QAEA;MAEA;MAEA,KAAAxD,aAAA,CAAA8E,SAAA,CAAA3B,MAAA;IACA;IAEA;IACAH,kBAAA,WAAAA,mBAAA;MACA,UAAAC,KAAA,CAAAhD,cAAA;MAEA,KAAAA,cAAA,GAAAnB,OAAA,CAAAoE,IAAA,MAAAD,KAAA,CAAAhD,cAAA;;MAEA;MACA,IAAA8E,KAAA;MACA,IAAAC,UAAA;MACA,IAAAC,SAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAC,IAAA;QACAD,IAAA,CAAAE,OAAA,CAAAF,IAAA,CAAAG,OAAA,KAAAJ,CAAA;QACAH,KAAA,CAAAQ,IAAA,CAAAJ,IAAA,CAAAK,kBAAA;UAAAC,KAAA;UAAAC,GAAA;QAAA;QACAV,UAAA,CAAAO,IAAA,CAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACAZ,SAAA,CAAAM,IAAA,CAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;MACA;MAEA,IAAA1C,MAAA;QACAV,KAAA;UACAW,IAAA;UACAC,IAAA;UACAC,SAAA;YACAC,QAAA;YACAC,KAAA;UACA;QACA;QACAC,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAC,MAAA;UACAR,IAAA;UACAC,SAAA;YACAC,QAAA;UACA;QACA;QACAuC,IAAA;UACAzC,IAAA;UACA0C,KAAA;UACAlC,MAAA;UACAmC,GAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAnC,IAAA;UACAoC,WAAA;UACAjH,IAAA,EAAA6F,KAAA;UACAqB,SAAA;YACA7C,QAAA;UACA;QACA;QACA8C,KAAA;UACAtC,IAAA;UACAqC,SAAA;YACA7C,QAAA;UACA;QACA;QACAO,MAAA,GACA;UACA7E,IAAA;UACA8E,IAAA;UACAuC,KAAA;UACAC,MAAA;UACAC,SAAA;YACAhD,KAAA;UACA;UACAW,SAAA;YACAX,KAAA;UACA;UACAiD,SAAA;YACAjD,KAAA;cACAO,IAAA;cACA2C,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA;gBACAC,MAAA;gBAAAvD,KAAA;cACA;gBACAuD,MAAA;gBAAAvD,KAAA;cACA;YACA;UACA;UACAtE,IAAA,EAAA8F;QACA,GACA;UACA/F,IAAA;UACA8E,IAAA;UACAwC,MAAA;UACAC,SAAA;YACAhD,KAAA;UACA;UACAW,SAAA;YACAX,KAAA;UACA;UACAtE,IAAA,EAAA+F;QACA;MAEA;MAEA,KAAAhF,cAAA,CAAA6E,SAAA,CAAA3B,MAAA;IACA;IAEA;IACAzC,YAAA,WAAAA,aAAA;MACA,SAAAV,aAAA;QACA,KAAAA,aAAA,CAAAgH,MAAA;MACA;MACA,SAAA/G,cAAA;QACA,KAAAA,cAAA,CAAA+G,MAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,UAAA;MACA,KAAAA,UAAA;MACA,IAAA/B,IAAA,OAAAC,IAAA,CAAA8B,UAAA;MACA,OAAA/B,IAAA,CAAAK,kBAAA;QACA2B,IAAA;QACA1B,KAAA;QACAC,GAAA;MACA;IACA;IAEA;IACA0B,SAAA,WAAAA,UAAA;MACA,IAAAC,GAAA,OAAAjC,IAAA;MACA,IAAAkC,SAAA,OAAAlC,IAAA;MACA,IAAAmC,IAAA,GAAAF,GAAA,GAAAC,SAAA;MACA,IAAAE,IAAA,GAAA7B,IAAA,CAAAC,KAAA,CAAA2B,IAAA;MACA,UAAAE,MAAA,CAAAD,IAAA;IACA;IAEA;IACAE,QAAA,WAAAA,SAAAC,IAAA;MACA,KAAAC,OAAA,CAAArC,IAAA,CAAAoC,IAAA;IACA;IAEA;IACAE,QAAA,WAAAA,SAAAC,IAAA;MACAtH,MAAA,CAAAuH,IAAA,CAAAD,IAAA;IACA;EACA;AACA", "ignoreList": []}]}