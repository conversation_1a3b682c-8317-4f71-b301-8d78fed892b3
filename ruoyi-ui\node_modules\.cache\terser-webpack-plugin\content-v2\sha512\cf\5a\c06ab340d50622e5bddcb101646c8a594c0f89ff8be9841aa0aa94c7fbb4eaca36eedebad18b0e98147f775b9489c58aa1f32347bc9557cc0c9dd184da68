{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2d0dd792\"],{8213:function(e,t,a){\"use strict\";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"职位名称\",prop:\"jobTitle\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入职位名称\",clearable:\"\",size:\"small\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobTitle,callback:function(t){e.$set(e.queryParams,\"jobTitle\",t)},expression:\"queryParams.jobTitle\"}})],1),a(\"el-form-item\",{attrs:{label:\"公司名称\",prop:\"companyName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入公司名称\",clearable:\"\",size:\"small\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.companyName,callback:function(t){e.$set(e.queryParams,\"companyName\",t)},expression:\"queryParams.companyName\"}})],1),a(\"el-form-item\",{attrs:{label:\"工作地点\",prop:\"workLocation\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入工作地点\",clearable:\"\",size:\"small\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.workLocation,callback:function(t){e.$set(e.queryParams,\"workLocation\",t)},expression:\"queryParams.workLocation\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\",size:\"small\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:add\"],expression:\"['miniapp:job:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:e.handleAdd}},[e._v(\"新增\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:edit\"],expression:\"['miniapp:job:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:remove\"],expression:\"['miniapp:job:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:export\"],expression:\"['miniapp:job:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.jobList},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"职位ID\",align:\"center\",prop:\"jobId\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"职位名称\",align:\"center\",prop:\"jobTitle\"}}),a(\"el-table-column\",{attrs:{label:\"公司名称\",align:\"center\",prop:\"companyName\"}}),a(\"el-table-column\",{attrs:{label:\"公司规模\",align:\"center\",prop:\"companyScale\",width:\"120\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"薪资范围\",align:\"center\",prop:\"salaryRange\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"工作地点\",align:\"center\",prop:\"workLocation\",width:\"100\"}}),a(\"el-table-column\",{attrs:{label:\"详细地址\",align:\"center\",prop:\"address\",width:\"150\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"职位标签\",align:\"center\",prop:\"jobTags\",width:\"180\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"排序\",align:\"center\",prop:\"sortOrder\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"dict-tag\",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:edit\"],expression:\"['miniapp:job:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:job:remove\"],expression:\"['miniapp:job:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"800px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"职位名称\",prop:\"jobTitle\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入职位名称\"},model:{value:e.form.jobTitle,callback:function(t){e.$set(e.form,\"jobTitle\",t)},expression:\"form.jobTitle\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"公司名称\",prop:\"companyName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入公司名称\"},model:{value:e.form.companyName,callback:function(t){e.$set(e.form,\"companyName\",t)},expression:\"form.companyName\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"公司规模\",prop:\"companyScale\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入公司规模\"},model:{value:e.form.companyScale,callback:function(t){e.$set(e.form,\"companyScale\",t)},expression:\"form.companyScale\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"薪资范围\",prop:\"salaryRange\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入薪资范围\"},model:{value:e.form.salaryRange,callback:function(t){e.$set(e.form,\"salaryRange\",t)},expression:\"form.salaryRange\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"工作地点\",prop:\"workLocation\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入工作地点\"},model:{value:e.form.workLocation,callback:function(t){e.$set(e.form,\"workLocation\",t)},expression:\"form.workLocation\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"职位标签\",prop:\"jobTags\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入职位标签，多个用逗号分隔\"},model:{value:e.form.jobTags,callback:function(t){e.$set(e.form,\"jobTags\",t)},expression:\"form.jobTags\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"职位描述\",prop:\"jobDescription\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入职位描述\"},model:{value:e.form.jobDescription,callback:function(t){e.$set(e.form,\"jobDescription\",t)},expression:\"form.jobDescription\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"任职要求\",prop:\"requirements\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入任职要求\"},model:{value:e.form.requirements,callback:function(t){e.$set(e.form,\"requirements\",t)},expression:\"form.requirements\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"详细地址\",prop:\"address\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入详细地址\"},model:{value:e.form.address,callback:function(t){e.$set(e.form,\"address\",t)},expression:\"form.address\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"联系方式\",prop:\"contactInfo\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入联系方式\"},model:{value:e.form.contactInfo,callback:function(t){e.$set(e.form,\"contactInfo\",t)},expression:\"form.contactInfo\"}})],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[a(\"el-input-number\",{attrs:{\"controls-position\":\"right\",min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1)],1),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},e._l(e.dict.type.sys_normal_disable,(function(t){return a(\"el-radio\",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1),a(\"el-row\",[a(\"el-col\",{attrs:{span:24}},[a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1)],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},o=[],r=a(\"5530\"),n=(a(\"d81d\"),a(\"d3b7\"),a(\"0643\"),a(\"a573\"),a(\"b775\"));function i(e){return Object(n[\"a\"])({url:\"/miniapp/job/list\",method:\"get\",params:e})}function s(e){return Object(n[\"a\"])({url:\"/miniapp/job/\"+e,method:\"get\"})}function c(e){return Object(n[\"a\"])({url:\"/miniapp/job\",method:\"post\",data:e})}function m(e){return Object(n[\"a\"])({url:\"/miniapp/job\",method:\"put\",data:e})}function p(e){return Object(n[\"a\"])({url:\"/miniapp/job/\"+e,method:\"delete\"})}var u={name:\"Job\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,jobList:[],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,jobTitle:null,companyName:null,workLocation:null,status:null},form:{},rules:{jobTitle:[{required:!0,message:\"职位名称不能为空\",trigger:\"blur\"}],companyName:[{required:!0,message:\"公司名称不能为空\",trigger:\"blur\"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.jobList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error(\"获取招聘职位列表失败:\",t),e.loading=!1,e.$modal.msgError(\"获取数据失败，请检查网络连接或联系管理员\")}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={jobId:null,jobTitle:null,companyName:null,companyScale:null,salaryRange:null,jobTags:null,workLocation:null,address:null,jobDescription:null,requirements:null,contactInfo:null,sortOrder:0,status:\"0\",remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加招聘职位\"},handleUpdate:function(e){var t=this;this.reset();var a=e.jobId||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改招聘职位\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.jobId?m(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.jobId||this.ids;this.$modal.confirm('是否确认删除招聘职位编号为\"'+a+'\"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/job/export\",Object(r[\"a\"])({},this.queryParams),\"job_\".concat((new Date).getTime(),\".xlsx\"))}}},d=u,b=a(\"2877\"),f=Object(b[\"a\"])(d,l,o,!1,null,null,null);t[\"default\"]=f.exports}}]);", "extractedComments": []}