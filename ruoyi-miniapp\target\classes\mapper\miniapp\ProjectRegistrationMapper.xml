<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.ProjectRegistrationMapper">
    
    <resultMap type="ProjectRegistration" id="ProjectRegistrationResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="teamSize"    column="team_size"    />
        <result property="city"    column="city"    />
        <result property="competitionArea"    column="competition_area"    />
        <result property="industry"    column="industry"    />
        <result property="isTjuAlumni"    column="is_tju_alumni"    />
        <result property="projectDescription"    column="project_description"    />
        <result property="hasCompany"    column="has_company"    />
        <result property="companyName"    column="company_name"    />
        <result property="lastYearRevenue"    column="last_year_revenue"    />
        <result property="projectValuation"    column="project_valuation"    />
        <result property="latestFundingRound"    column="latest_funding_round"    />
        <result property="investmentInstitution"    column="investment_institution"    />
        <result property="companyLogo"    column="company_logo"    />
        <result property="projectBp"    column="project_bp"    />
        <result property="recommender"    column="recommender"    />
        <result property="sponsorUnit"    column="sponsor_unit"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactWechat"    column="contact_wechat"    />
        <result property="contactPosition"    column="contact_position"    />
        <result property="registrationTime"    column="registration_time"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectProjectRegistrationVo">
        select id, user_id, project_name, team_size, city, competition_area, industry, is_tju_alumni, project_description, has_company, company_name, last_year_revenue, project_valuation, latest_funding_round, investment_institution, company_logo, project_bp, recommender, sponsor_unit, contact_name, contact_phone, contact_wechat, contact_position, registration_time, created_at, updated_at from mini_project_registration
    </sql>

    <select id="selectProjectRegistrationList" parameterType="ProjectRegistration" resultMap="ProjectRegistrationResult">
        <include refid="selectProjectRegistrationVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="teamSize != null "> and team_size = #{teamSize}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="competitionArea != null  and competitionArea != ''"> and competition_area = #{competitionArea}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="isTjuAlumni != null "> and is_tju_alumni = #{isTjuAlumni}</if>
            <if test="hasCompany != null "> and has_company = #{hasCompany}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="registrationTime != null "> and date_format(registration_time,'%y%m%d') = date_format(#{registrationTime},'%y%m%d')</if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectProjectRegistrationById" parameterType="Long" resultMap="ProjectRegistrationResult">
        <include refid="selectProjectRegistrationVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectRegistration" parameterType="ProjectRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into mini_project_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            project_name,
            team_size,
            city,
            competition_area,
            industry,
            is_tju_alumni,
            project_description,
            has_company,
            contact_name,
            contact_phone,
            contact_wechat,
            contact_position,
            <if test="companyName != null">company_name,</if>
            <if test="lastYearRevenue != null">last_year_revenue,</if>
            <if test="projectValuation != null">project_valuation,</if>
            <if test="latestFundingRound != null">latest_funding_round,</if>
            <if test="investmentInstitution != null">investment_institution,</if>
            <if test="companyLogo != null">company_logo,</if>
            <if test="projectBp != null">project_bp,</if>
            <if test="recommender != null">recommender,</if>
            <if test="sponsorUnit != null">sponsor_unit,</if>
            <if test="registrationTime != null">registration_time,</if>
            <if test="createTime != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            #{projectName},
            #{teamSize},
            #{city},
            #{competitionArea},
            #{industry},
            #{isTjuAlumni},
            #{projectDescription},
            #{hasCompany},
            #{contactName},
            #{contactPhone},
            #{contactWechat},
            #{contactPosition},
            <if test="companyName != null">#{companyName},</if>
            <if test="lastYearRevenue != null">#{lastYearRevenue},</if>
            <if test="projectValuation != null">#{projectValuation},</if>
            <if test="latestFundingRound != null">#{latestFundingRound},</if>
            <if test="investmentInstitution != null">#{investmentInstitution},</if>
            <if test="companyLogo != null">#{companyLogo},</if>
            <if test="projectBp != null">#{projectBp},</if>
            <if test="recommender != null">#{recommender},</if>
            <if test="sponsorUnit != null">#{sponsorUnit},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateProjectRegistration" parameterType="ProjectRegistration">
        update mini_project_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="teamSize != null">team_size = #{teamSize},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="competitionArea != null and competitionArea != ''">competition_area = #{competitionArea},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="isTjuAlumni != null">is_tju_alumni = #{isTjuAlumni},</if>
            <if test="projectDescription != null and projectDescription != ''">project_description = #{projectDescription},</if>
            <if test="hasCompany != null">has_company = #{hasCompany},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="lastYearRevenue != null">last_year_revenue = #{lastYearRevenue},</if>
            <if test="projectValuation != null">project_valuation = #{projectValuation},</if>
            <if test="latestFundingRound != null">latest_funding_round = #{latestFundingRound},</if>
            <if test="investmentInstitution != null">investment_institution = #{investmentInstitution},</if>
            <if test="companyLogo != null">company_logo = #{companyLogo},</if>
            <if test="projectBp != null">project_bp = #{projectBp},</if>
            <if test="recommender != null">recommender = #{recommender},</if>
            <if test="sponsorUnit != null">sponsor_unit = #{sponsorUnit},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactWechat != null and contactWechat != ''">contact_wechat = #{contactWechat},</if>
            <if test="contactPosition != null and contactPosition != ''">contact_position = #{contactPosition},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
            <if test="updateTime != null">updated_at = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectRegistrationById" parameterType="Long">
        delete from mini_project_registration where id = #{id}
    </delete>

    <delete id="deleteProjectRegistrationByIds" parameterType="String">
        delete from mini_project_registration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper> 