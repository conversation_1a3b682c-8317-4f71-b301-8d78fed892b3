{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=template&id=35a4206d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}