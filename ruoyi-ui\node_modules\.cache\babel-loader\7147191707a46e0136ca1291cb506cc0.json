{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1754037225575}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_project", "require", "_ImageUpload", "_interopRequireDefault", "name", "components", "ImageUpload", "data", "loading", "ids", "multiple", "showSearch", "total", "projectList", "sponsorOpen", "viewOpen", "queryParams", "pageNum", "pageSize", "projectName", "city", "industry", "sponsorForm", "sponsorUnit", "viewForm", "created", "getList", "methods", "_this", "listProject", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleSponsorUpload", "_this2", "getSponsorImage", "catch", "error", "cancelSponsor", "submitSponsorForm", "_this3", "updateSponsorImage", "$modal", "msgSuccess", "msgError", "handleView", "row", "handleDelete", "_this4", "confirm", "delProject", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/miniapp/haitang/project/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"城市\" prop=\"city\">\r\n        <el-input\r\n          v-model=\"queryParams.city\"\r\n          placeholder=\"请输入城市\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"行业\" prop=\"industry\">\r\n        <el-input\r\n          v-model=\"queryParams.industry\"\r\n          placeholder=\"请输入行业\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-picture\"\r\n          size=\"mini\"\r\n          @click=\"handleSponsorUpload\"\r\n          v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n        >赞助商图片</el-button>\r\n      </el-col>\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:haitang:project:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"projectList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"团队规模\" align=\"center\" prop=\"teamSize\" />\r\n      <el-table-column label=\"城市\" align=\"center\" prop=\"city\" />\r\n      <el-table-column label=\"赛区\" align=\"center\" prop=\"competitionArea\" />\r\n      <el-table-column label=\"行业\" align=\"center\" prop=\"industry\" />\r\n      <el-table-column label=\"天大校友\" align=\"center\" prop=\"isTjuAlumni\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.isTjuAlumni ? 'success' : 'info'\">\r\n            {{ scope.row.isTjuAlumni ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有公司\" align=\"center\" prop=\"hasCompany\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.hasCompany ? 'success' : 'info'\">\r\n            {{ scope.row.hasCompany ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"公司名称\" align=\"center\" prop=\"companyName\" :show-overflow-tooltip=\"true\"/>\r\n      <el-table-column label=\"公司Logo\" align=\"center\" prop=\"companyLogo\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-image\r\n            v-if=\"scope.row.companyLogo\"\r\n            :src=\"scope.row.companyLogo\"\r\n            :preview-src-list=\"[scope.row.companyLogo]\"\r\n            style=\"width: 50px; height: 50px\"\r\n            fit=\"cover\"\r\n          />\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactName\" />\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:query']\"\r\n          >查看</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:project:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorOpen\" width=\"700px\" append-to-body>\r\n      <el-form ref=\"sponsorForm\" :model=\"sponsorForm\" label-width=\"120px\">\r\n        <el-form-item label=\"当前赞助商图片\">\r\n          <div v-if=\"sponsorForm.sponsorUnit && sponsorForm.sponsorUnit.trim() !== ''\" style=\"padding: 10px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-success\" style=\"color: #67c23a; margin-right: 5px;\"></i>\r\n            <span style=\"color: #409eff;\">已设置赞助商图片</span>\r\n          </div>\r\n          <div v-else style=\"padding: 10px; background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; margin-bottom: 10px;\">\r\n            <i class=\"el-icon-warning\" style=\"color: #e6a23c; margin-right: 5px;\"></i>\r\n            <span style=\"color: #e6a23c;\">暂未设置赞助商图片</span>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"上传新图片\">\r\n          <image-upload v-model=\"sponsorForm.sponsorUnit\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitSponsorForm\">确 定</el-button>\r\n        <el-button @click=\"cancelSponsor\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog title=\"项目报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"项目名称\">{{ viewForm.projectName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"团队规模\">{{ viewForm.teamSize }}人</el-descriptions-item>\r\n        <el-descriptions-item label=\"城市\">{{ viewForm.city }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赛区\">{{ viewForm.competitionArea }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"行业\">{{ viewForm.industry }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"天大校友\">{{ viewForm.isTjuAlumni ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目描述\" :span=\"2\">{{ viewForm.projectDescription }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"是否有公司\">{{ viewForm.hasCompany ? '是' : '否' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司名称\" v-if=\"viewForm.hasCompany\">{{ viewForm.companyName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"去年营收\" v-if=\"viewForm.hasCompany\">{{ viewForm.lastYearRevenue }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"项目估值\" v-if=\"viewForm.hasCompany\">{{ viewForm.projectValuation }}万元</el-descriptions-item>\r\n        <el-descriptions-item label=\"最新融资轮次\" v-if=\"viewForm.hasCompany\">{{ viewForm.latestFundingRound }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"投资机构\" v-if=\"viewForm.hasCompany\">{{ viewForm.investmentInstitution }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"公司logo\" v-if=\"viewForm.hasCompany && viewForm.companyLogo\">\r\n          <el-image\r\n            style=\"width: 100px; height: 60px\"\r\n            :src=\"viewForm.companyLogo\"\r\n            :preview-src-list=\"[viewForm.companyLogo]\"\r\n            fit=\"cover\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"项目BP\" v-if=\"viewForm.projectBp\">\r\n          <el-link type=\"primary\" :href=\"viewForm.projectBp\" target=\"_blank\" :underline=\"false\">\r\n            <i class=\"el-icon-download\"></i> 下载项目BP\r\n          </el-link>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"推荐人\">{{ viewForm.recommender }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赞助单位\" v-if=\"viewForm.sponsorUnit\">\r\n          <el-image\r\n            style=\"width: 120px; height: 60px\"\r\n            :src=\"viewForm.sponsorUnit\"\r\n            :preview-src-list=\"[viewForm.sponsorUnit]\"\r\n            fit=\"contain\">\r\n          </el-image>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人姓名\">{{ viewForm.contactName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人电话\">{{ viewForm.contactPhone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人微信\">{{ viewForm.contactWechat }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"联系人职位\">{{ viewForm.contactPosition }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listProject, delProject, updateSponsorImage, getSponsorImage } from \"@/api/miniapp/haitang/project\";\r\nimport ImageUpload from \"@/components/ImageUpload\";\r\n\r\nexport default {\r\n  name: \"Project\",\r\n  components: {\r\n    ImageUpload\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目报名表格数据\r\n      projectList: [],\r\n      // 是否显示赞助商图片弹出层\r\n      sponsorOpen: false,\r\n      // 是否显示查看详情弹出层\r\n      viewOpen: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectName: null,\r\n        city: null,\r\n        industry: null\r\n      },\r\n      // 赞助商表单参数\r\n      sponsorForm: {\r\n        sponsorUnit: null\r\n      },\r\n      // 查看详情表单参数\r\n      viewForm: {}\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询项目报名列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProject(this.queryParams).then(response => {\r\n        this.projectList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      getSponsorImage().then(response => {\r\n        this.sponsorForm.sponsorUnit = response.data || '';\r\n        this.sponsorOpen = true;\r\n      }).catch(error => {\r\n        this.sponsorForm.sponsorUnit = '';\r\n        this.sponsorOpen = true;\r\n      });\r\n    },\r\n    /** 取消赞助商图片上传 */\r\n    cancelSponsor() {\r\n      this.sponsorOpen = false;\r\n      // 不要清空数据，保持原有数据\r\n      // this.sponsorForm.sponsorUnit = null;\r\n    },\r\n    /** 提交赞助商图片 */\r\n    submitSponsorForm() {\r\n      updateSponsorImage(this.sponsorForm.sponsorUnit).then(response => {\r\n        this.$modal.msgSuccess(\"赞助商图片更新成功\");\r\n        this.sponsorOpen = false;\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"更新赞助商图片失败\");\r\n      });\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.viewForm = row;\r\n      this.viewOpen = true;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除项目报名编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delProject(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/project/export', {\r\n        ...this.queryParams\r\n      }, `project_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA2NA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACA;MACAC,WAAA;QACAC,WAAA;MACA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,oBAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAf,WAAA,GAAAkB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAgB,KAAA,CAAApB,OAAA;MACA;IACA;IAEA,aACAyB,WAAA,WAAAA,YAAA;MACA,KAAAjB,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5B,GAAA,GAAA4B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9B,QAAA,IAAA2B,SAAA,CAAAI,MAAA;IACA;IAEA,kBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,wBAAA,IAAAd,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAArB,WAAA,CAAAC,WAAA,GAAAQ,QAAA,CAAAxB,IAAA;QACAoC,MAAA,CAAA7B,WAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAH,MAAA,CAAArB,WAAA,CAAAC,WAAA;QACAoB,MAAA,CAAA7B,WAAA;MACA;IACA;IACA,gBACAiC,aAAA,WAAAA,cAAA;MACA,KAAAjC,WAAA;MACA;MACA;IACA;IACA,cACAkC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,2BAAA,OAAA5B,WAAA,CAAAC,WAAA,EAAAO,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAAE,MAAA,CAAAC,UAAA;QACAH,MAAA,CAAAnC,WAAA;MACA,GAAA+B,KAAA,WAAAC,KAAA;QACAG,MAAA,CAAAE,MAAA,CAAAE,QAAA;MACA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA/B,QAAA,GAAA+B,GAAA;MACA,KAAAxC,QAAA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAhD,GAAA,GAAA8C,GAAA,CAAAf,EAAA,SAAA/B,GAAA;MACA,KAAA0C,MAAA,CAAAO,OAAA,oBAAAjD,GAAA,aAAAqB,IAAA;QACA,WAAA6B,mBAAA,EAAAlD,GAAA;MACA,GAAAqB,IAAA;QACA2B,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAN,MAAA,CAAAC,UAAA;MACA,GAAAP,KAAA;IACA;IACA,aACAe,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,uCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA/C,WAAA,cAAAgD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}