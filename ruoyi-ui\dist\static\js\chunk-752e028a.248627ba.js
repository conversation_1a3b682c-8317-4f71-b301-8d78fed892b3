(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-752e028a"],{"1e4b":function(t,s,a){"use strict";a.r(s);var e=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"app-container home"},[a("div",{staticClass:"welcome-banner"},[a("div",{staticClass:"banner-content"},[t._m(0),a("p",{staticClass:"banner-subtitle"},[t._v("智慧校园 · 创新服务 · 数据驱动")]),a("div",{staticClass:"banner-stats"},[a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.totalUsers))]),a("div",{staticClass:"stat-label"},[t._v("系统用户")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.activeUsers))]),a("div",{staticClass:"stat-label"},[t._v("活跃用户")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.totalEnterprises))]),a("div",{staticClass:"stat-label"},[t._v("入驻企业")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.todayOperations))]),a("div",{staticClass:"stat-label"},[t._v("今日操作")])])])])]),a("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[a("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[a("div",{staticClass:"stat-card activities"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-star-on"})]),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.totalActivities))]),a("div",{staticClass:"stat-title"},[t._v("精彩活动")])])])]),a("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[a("div",{staticClass:"stat-card demands"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-connection"})]),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.totalDemands))]),a("div",{staticClass:"stat-title"},[t._v("需求对接")])])])]),a("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[a("div",{staticClass:"stat-card events"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-tickets"})]),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.totalEvents))]),a("div",{staticClass:"stat-title"},[t._v("活动报名")])])])]),a("el-col",{attrs:{xs:12,sm:6,md:6,lg:6,xl:6}},[a("div",{staticClass:"stat-card experts"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-user"})]),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.totalExperts))]),a("div",{staticClass:"stat-title"},[t._v("专家库")])])])])],1),a("el-row",{staticClass:"charts-row",attrs:{gutter:20}},[a("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-pie-chart"}),t._v(" 业务数据分布")])]),a("div",{staticClass:"chart-container"},[a("div",{ref:"businessChart",staticClass:"chart"})])])],1),a("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-data-line"}),t._v(" 用户活跃度趋势")])]),a("div",{staticClass:"chart-container"},[a("div",{ref:"userTrendChart",staticClass:"chart"})])])],1)],1),a("el-row",{staticClass:"main-content",attrs:{gutter:20}},[a("el-col",{attrs:{xs:24,sm:24,md:8,lg:8}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-star-on"}),t._v(" 最新活动")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/activity")}}},[t._v("查看更多")])],1),a("div",{staticClass:"activity-list"},[t._l(t.recentActivities,(function(s){return a("div",{key:s.id,staticClass:"activity-item"},[a("div",{staticClass:"activity-title"},[t._v(t._s(s.title))]),a("div",{staticClass:"activity-time"},[t._v(t._s(t.formatDate(s.create_time)))])])})),0===t.recentActivities.length?a("div",{staticClass:"empty-data"},[a("i",{staticClass:"el-icon-document"}),a("p",[t._v("暂无活动数据")])]):t._e()],2)])],1),a("el-col",{attrs:{xs:24,sm:24,md:8,lg:8}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-connection"}),t._v(" 最新需求")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/demand")}}},[t._v("查看更多")])],1),a("div",{staticClass:"demand-list"},[t._l(t.recentDemands,(function(s){return a("div",{key:s.id,staticClass:"demand-item"},[a("div",{staticClass:"demand-title"},[t._v(t._s(s.demand_title))]),a("div",{staticClass:"demand-time"},[t._v(t._s(t.formatDate(s.create_time)))])])})),0===t.recentDemands.length?a("div",{staticClass:"empty-data"},[a("i",{staticClass:"el-icon-document"}),a("p",[t._v("暂无需求数据")])]):t._e()],2)])],1),a("el-col",{attrs:{xs:24,sm:24,md:8,lg:8}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-monitor"}),t._v(" 系统状态")])]),a("div",{staticClass:"system-status"},[a("div",{staticClass:"status-item"},[a("div",{staticClass:"status-icon online"},[a("i",{staticClass:"el-icon-success"})]),a("div",{staticClass:"status-info"},[a("div",{staticClass:"status-label"},[t._v("系统状态")]),a("div",{staticClass:"status-value"},[t._v("运行正常")])])]),a("div",{staticClass:"status-item"},[a("div",{staticClass:"status-icon"},[a("i",{staticClass:"el-icon-time"})]),a("div",{staticClass:"status-info"},[a("div",{staticClass:"status-label"},[t._v("运行时间")]),a("div",{staticClass:"status-value"},[t._v(t._s(t.getUptime()))])])]),a("div",{staticClass:"status-item"},[a("div",{staticClass:"status-icon"},[a("i",{staticClass:"el-icon-view"})]),a("div",{staticClass:"status-info"},[a("div",{staticClass:"status-label"},[t._v("活跃用户")]),a("div",{staticClass:"status-value"},[t._v(t._s(t.activeUsers)+" 人")])])])])])],1)],1),a("el-row",{staticClass:"main-content",attrs:{gutter:20}},[a("el-col",{attrs:{xs:24,sm:24,md:24,lg:24}},[a("el-card",{staticClass:"content-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[a("i",{staticClass:"el-icon-s-operation"}),t._v(" 快捷操作")])]),a("div",{staticClass:"quick-actions"},[a("div",{staticClass:"action-group"},[a("h4",[t._v("内容管理")]),a("div",{staticClass:"action-buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/activity")}}},[t._v(" 新增活动 ")]),a("el-button",{attrs:{type:"success",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/banner")}}},[t._v(" 新增轮播图 ")]),a("el-button",{attrs:{type:"info",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/notice")}}},[t._v(" 新增通知 ")]),a("el-button",{attrs:{type:"warning",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/news")}}},[t._v(" 新增新闻 ")])],1)]),a("div",{staticClass:"action-group"},[a("h4",[t._v("用户服务")]),a("div",{staticClass:"action-buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/demand")}}},[t._v(" 发布需求 ")]),a("el-button",{attrs:{type:"success",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/event")}}},[t._v(" 创建活动 ")]),a("el-button",{attrs:{type:"info",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/job")}}},[t._v(" 发布职位 ")]),a("el-button",{attrs:{type:"warning",icon:"el-icon-plus",size:"small"},on:{click:function(s){return t.goToPage("/miniapp/expert")}}},[t._v(" 添加专家 ")])],1)]),a("div",{staticClass:"action-group"},[a("h4",[t._v("系统管理")]),a("div",{staticClass:"action-buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-user",size:"small"},on:{click:function(s){return t.goToPage("/system/user")}}},[t._v(" 用户管理 ")]),a("el-button",{attrs:{type:"success",icon:"el-icon-setting",size:"small"},on:{click:function(s){return t.goToPage("/system/menu")}}},[t._v(" 菜单管理 ")]),a("el-button",{attrs:{type:"info",icon:"el-icon-view",size:"small"},on:{click:function(s){return t.goToPage("/monitor/operlog")}}},[t._v(" 操作日志 ")]),a("el-button",{attrs:{type:"warning",icon:"el-icon-monitor",size:"small"},on:{click:function(s){return t.goToPage("/monitor/server")}}},[t._v(" 服务监控 ")])],1)])])])],1)],1)],1)},i=[function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("h1",{staticClass:"banner-title"},[a("i",{staticClass:"el-icon-star-on"}),t._v(" 天津大学海棠小程序管理后台 ")])}],n=a("c14f"),l=a("1da1"),c=(a("14d9"),a("313e")),o={name:"Index",data:function(){return{version:"3.9.0",totalUsers:0,activeUsers:0,totalActivities:0,totalDemands:0,totalEvents:0,totalEnterprises:0,totalJobs:0,totalExperts:0,totalNews:0,todayOperations:0,recentActivities:[],recentDemands:[],businessChart:null,userTrendChart:null}},created:function(){this.loadDashboardData()},mounted:function(){var t=this;this.$nextTick((function(){t.initCharts(),window.addEventListener("resize",t.handleResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize),this.businessChart&&this.businessChart.dispose(),this.userTrendChart&&this.userTrendChart.dispose()},methods:{loadDashboardData:function(){var t=this;return Object(l["a"])(Object(n["a"])().m((function s(){var a;return Object(n["a"])().w((function(s){while(1)switch(s.n){case 0:return s.p=0,s.n=1,t.loadStatistics();case 1:return s.n=2,t.loadRecentActivities();case 2:return s.n=3,t.loadRecentDemands();case 3:s.n=5;break;case 4:s.p=4,a=s.v,console.error("加载仪表盘数据失败:",a),t.setDefaultData();case 5:return s.a(2)}}),s,null,[[0,4]])})))()},loadStatistics:function(){var t=this;return Object(l["a"])(Object(n["a"])().m((function s(){return Object(n["a"])().w((function(s){while(1)switch(s.n){case 0:t.totalUsers=6,t.activeUsers=4,t.totalActivities=2,t.totalDemands=2,t.totalEvents=4,t.totalEnterprises=2,t.totalJobs=8,t.totalExperts=4,t.totalNews=2,t.todayOperations=36;case 1:return s.a(2)}}),s)})))()},loadRecentActivities:function(){var t=this;return Object(l["a"])(Object(n["a"])().m((function s(){return Object(n["a"])().w((function(s){while(1)switch(s.n){case 0:t.recentActivities=[{id:1,title:"测试页",create_time:"2025-07-18T01:39:58.000Z"},{id:2,title:"江西宇悦科技有限公司",create_time:"2025-07-16T03:14:25.000Z"}];case 1:return s.a(2)}}),s)})))()},loadRecentDemands:function(){var t=this;return Object(l["a"])(Object(n["a"])().m((function s(){return Object(n["a"])().w((function(s){while(1)switch(s.n){case 0:t.recentDemands=[{id:1,demand_title:"寻球寻求寻求",create_time:"2025-07-30T07:43:00.000Z"},{id:2,demand_title:"我是夏宇杰",create_time:"2025-07-30T06:55:45.000Z"}];case 1:return s.a(2)}}),s)})))()},setDefaultData:function(){this.totalUsers=0,this.activeUsers=0,this.totalActivities=0,this.totalDemands=0,this.totalEvents=0,this.totalEnterprises=0,this.totalJobs=0,this.totalExperts=0,this.totalNews=0,this.todayOperations=0,this.recentActivities=[],this.recentDemands=[]},initCharts:function(){this.initBusinessChart(),this.initUserTrendChart()},initBusinessChart:function(){if(this.$refs.businessChart){this.businessChart=c["init"](this.$refs.businessChart);var t={title:{text:"业务模块分布",left:"center",textStyle:{fontSize:14,color:"#333"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{bottom:"5%",left:"center",textStyle:{fontSize:12}},series:[{name:"业务数据",type:"pie",radius:["40%","70%"],center:["50%","45%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:this.totalActivities,name:"精彩活动",itemStyle:{color:"#409eff"}},{value:this.totalDemands,name:"需求对接",itemStyle:{color:"#67c23a"}},{value:this.totalEvents,name:"活动报名",itemStyle:{color:"#e6a23c"}},{value:this.totalExperts,name:"专家库",itemStyle:{color:"#f56c6c"}},{value:this.totalJobs,name:"招聘职位",itemStyle:{color:"#909399"}},{value:this.totalNews,name:"新闻资讯",itemStyle:{color:"#606266"}}]}]};this.businessChart.setOption(t)}},initUserTrendChart:function(){if(this.$refs.userTrendChart){this.userTrendChart=c["init"](this.$refs.userTrendChart);for(var t=[],s=[],a=[],e=6;e>=0;e--){var i=new Date;i.setDate(i.getDate()-e),t.push(i.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})),s.push(Math.floor(5*Math.random())+2),a.push(Math.floor(3*Math.random())+6)}var n={title:{text:"用户活跃度（最近7天）",left:"center",textStyle:{fontSize:14,color:"#333"}},tooltip:{trigger:"axis"},legend:{bottom:"5%",left:"center",textStyle:{fontSize:12}},grid:{left:"3%",right:"4%",bottom:"15%",top:"20%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:t,axisLabel:{fontSize:11}},yAxis:{type:"value",axisLabel:{fontSize:11}},series:[{name:"活跃用户",type:"line",stack:"Total",smooth:!0,lineStyle:{color:"#409eff"},itemStyle:{color:"#409eff"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},data:s},{name:"总用户",type:"line",smooth:!0,lineStyle:{color:"#67c23a"},itemStyle:{color:"#67c23a"},data:a}]};this.userTrendChart.setOption(n)}},handleResize:function(){this.businessChart&&this.businessChart.resize(),this.userTrendChart&&this.userTrendChart.resize()},formatDate:function(t){if(!t)return"";var s=new Date(t);return s.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},getUptime:function(){var t=new Date,s=new Date("2025-01-01"),a=t-s,e=Math.floor(a/864e5);return"".concat(e," 天")},goToPage:function(t){this.$router.push(t)},goTarget:function(t){window.open(t,"_blank")}}},r=o,d=(a("5790"),a("2877")),u=Object(d["a"])(r,e,i,!1,null,"d4eca012",null);s["default"]=u.exports},"532a":function(t,s,a){},5790:function(t,s,a){"use strict";a("532a")}}]);