package com.ruoyi.miniapp.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.miniapp.domain.MiniTechStar;

/**
 * 科技之星Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniTechStarMapper 
{
    /**
     * 查询科技之星
     * 
     * @param starId 科技之星主键
     * @return 科技之星
     */
    public MiniTechStar selectMiniTechStarByStarId(Long starId);

    /**
     * 查询科技之星列表
     * 
     * @param miniTechStar 科技之星
     * @return 科技之星集合
     */
    public List<MiniTechStar> selectMiniTechStarList(MiniTechStar miniTechStar);

    /**
     * 新增科技之星
     * 
     * @param miniTechStar 科技之星
     * @return 结果
     */
    public int insertMiniTechStar(MiniTechStar miniTechStar);

    /**
     * 修改科技之星
     * 
     * @param miniTechStar 科技之星
     * @return 结果
     */
    public int updateMiniTechStar(MiniTechStar miniTechStar);

    /**
     * 删除科技之星
     * 
     * @param starId 科技之星主键
     * @return 结果
     */
    public int deleteMiniTechStarByStarId(Long starId);

    /**
     * 批量删除科技之星
     * 
     * @param starIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniTechStarByStarIds(Long[] starIds);

    /**
     * 查询启用的科技之星列表（小程序端调用）
     * 
     * @return 科技之星集合
     */
    public List<MiniTechStar> selectEnabledMiniTechStarList();

    /**
     * 查询推荐的科技之星列表
     *
     * @return 科技之星集合
     */
    public List<MiniTechStar> selectRecommendedMiniTechStarList();

    /**
     * 增加科技之星浏览次数
     *
     * @param starId 科技之星主键
     * @return 结果
     */
    public int incrementViewCount(Long starId);
}