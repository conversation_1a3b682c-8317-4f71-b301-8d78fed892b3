{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955784453}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751437913992}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5ob21lIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOw0KDQogIC8vIOasoui/juaoquW5heagt+W8jw0KICAud2VsY29tZS1iYW5uZXIgew0KICAgIGJhY2tncm91bmQ6ICM0MDllZmY7DQogICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgIHBhZGRpbmc6IDMwcHg7DQogICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICBjb2xvcjogd2hpdGU7DQogICAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4zKTsNCg0KICAgIC5iYW5uZXItdGl0bGUgew0KICAgICAgZm9udC1zaXplOiAyOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIG1hcmdpbjogMCAwIDhweCAwOw0KDQogICAgICBpIHsNCiAgICAgICAgY29sb3I6ICNmZmQ3MDA7DQogICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5iYW5uZXItc3VidGl0bGUgew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgb3BhY2l0eTogMC45Ow0KICAgICAgbWFyZ2luOiAwIDAgMjVweCAwOw0KICAgIH0NCg0KICAgIC5iYW5uZXItc3RhdHMgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGdhcDogMzBweDsNCiAgICAgIGZsZXgtd3JhcDogd3JhcDsNCg0KICAgICAgLnN0YXQtaXRlbSB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgICAgICAuc3RhdC1udW1iZXIgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgb3BhY2l0eTogMC44Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g57uf6K6h5Y2h54mH5qC35byPDQogIC5zdGF0cy1yb3cgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAuc3RhdC1jYXJkIHsNCiAgICAgIGJhY2tncm91bmQ6IHdoaXRlOw0KICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsMCwwLDAuMSk7DQogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KDQogICAgICAmOmhvdmVyIHsNCiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICAgICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwwLDAsMC4xNSk7DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWljb24gew0KICAgICAgICB3aWR0aDogNjBweDsNCiAgICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQoNCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuc3RhdC1pbmZvIHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuc3RhdC12YWx1ZSB7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtdGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmFjdGl2aXRpZXMgew0KICAgICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNDA5ZWZmOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LXZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogIzQwOWVmZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmRlbWFuZHMgew0KICAgICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNjdjMjNhOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LXZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogIzY3YzIzYTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmV2ZW50cyB7DQogICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNlNmEyM2M7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjZTZhMjNjOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgICYuZXhwZXJ0cyB7DQogICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNmNTZjNmM7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjZjU2YzZjOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5Zu+6KGo5bGV56S65Yy65Z+f5qC35byPDQogIC5jaGFydHMtcm93IHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgLmNoYXJ0LWNvbnRhaW5lciB7DQogICAgICBwYWRkaW5nOiAxMHB4IDA7DQoNCiAgICAgIC5jaGFydCB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOS4u+imgeWGheWuueWMuuWfn+agt+W8jw0KICAubWFpbi1jb250ZW50IHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgLmNvbnRlbnQtY2FyZCB7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwwLDAsMC4xKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCg0KICAgICAgLmNhcmQtaGVhZGVyIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KDQogICAgICAgIGkgew0KICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgICAgIGNvbG9yOiAjNDA5ZWZmOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOa0u+WKqOWIl+ihqOagt+W8jw0KICAgICAgLmFjdGl2aXR5LWxpc3QsIC5kZW1hbmQtbGlzdCB7DQogICAgICAgIC5hY3Rpdml0eS1pdGVtLCAuZGVtYW5kLWl0ZW0gew0KICAgICAgICAgIHBhZGRpbmc6IDEycHggMDsNCiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5hY3Rpdml0eS10aXRsZSwgLmRlbWFuZC10aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAgICAgICAgICAgbGluZS1jbGFtcDogMjsNCiAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmFjdGl2aXR5LXRpbWUsIC5kZW1hbmQtdGltZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g57O757uf54q25oCB5qC35byPDQogICAgICAuc3lzdGVtLXN0YXR1cyB7DQogICAgICAgIC5zdGF0dXMtaXRlbSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIHBhZGRpbmc6IDE1cHggMDsNCiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0dXMtaWNvbiB7DQogICAgICAgICAgICB3aWR0aDogNDBweDsNCiAgICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KDQogICAgICAgICAgICBpIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzkwOTM5OTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgJi5vbmxpbmUgaSB7DQogICAgICAgICAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0dXMtaW5mbyB7DQogICAgICAgICAgICBmbGV4OiAxOw0KDQogICAgICAgICAgICAuc3RhdHVzLWxhYmVsIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAuc3RhdHVzLXZhbHVlIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOepuuaVsOaNruagt+W8jw0KICAgICAgLmVtcHR5LWRhdGEgew0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIHBhZGRpbmc6IDQwcHggMDsNCiAgICAgICAgY29sb3I6ICM5OTk7DQoNCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiA0OHB4Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgIH0NCg0KICAgICAgICBwIHsNCiAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5b+r5o235pON5L2c5qC35byPDQogIC5xdWljay1hY3Rpb25zIHsNCiAgICAuYWN0aW9uLWdyb3VwIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICB9DQoNCiAgICAgIGg0IHsNCiAgICAgICAgbWFyZ2luOiAwIDAgMTVweCAwOw0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDllZmY7DQogICAgICAgIHBhZGRpbmctbGVmdDogMTBweDsNCiAgICAgIH0NCg0KICAgICAgLmFjdGlvbi1idXR0b25zIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZ2FwOiAxMHB4Ow0KICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQoNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5ZON5bqU5byP6K6+6K6hDQogIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAgIHBhZGRpbmc6IDEwcHg7DQoNCiAgICAud2VsY29tZS1iYW5uZXIgew0KICAgICAgcGFkZGluZzogMjBweDsNCg0KICAgICAgLmJhbm5lci10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMjJweDsNCiAgICAgIH0NCg0KICAgICAgLmJhbm5lci1zdGF0cyB7DQogICAgICAgIGdhcDogMjBweDsNCg0KICAgICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgICAuc3RhdC1udW1iZXIgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5zdGF0cy1yb3cgew0KICAgICAgLnN0YXQtY2FyZCB7DQogICAgICAgIHBhZGRpbmc6IDE1cHg7DQoNCiAgICAgICAgLnN0YXQtaWNvbiB7DQogICAgICAgICAgd2lkdGg6IDUwcHg7DQogICAgICAgICAgaGVpZ2h0OiA1MHB4Ow0KDQogICAgICAgICAgaSB7DQogICAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtaW5mbyB7DQogICAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jaGFydHMtcm93IHsNCiAgICAgIC5jaGFydC1jb250YWluZXIgew0KICAgICAgICAuY2hhcnQgew0KICAgICAgICAgIGhlaWdodDogMjUwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAucXVpY2stYWN0aW9ucyB7DQogICAgICAuYWN0aW9uLWdyb3VwIHsNCiAgICAgICAgLmFjdGlvbi1idXR0b25zIHsNCiAgICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIHBhZGRpbmc6IDZweCAxMnB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+kBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"app-container home\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <div class=\"banner-content\">\r\n        <h1 class=\"banner-title\">\r\n          <i class=\"el-icon-star-on\"></i>\r\n          天津大学海棠小程序管理后台\r\n        </h1>\r\n        <p class=\"banner-subtitle\">智慧校园 · 创新服务 · 数据驱动</p>\r\n        <div class=\"banner-stats\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalUsers }}</div>\r\n            <div class=\"stat-label\">系统用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ activeUsers }}</div>\r\n            <div class=\"stat-label\">活跃用户</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ totalEnterprises }}</div>\r\n            <div class=\"stat-label\">入驻企业</div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-number\">{{ todayOperations }}</div>\r\n            <div class=\"stat-label\">今日操作</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据统计卡片 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card activities\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-star-on\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalActivities }}</div>\r\n            <div class=\"stat-title\">精彩活动</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card demands\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-connection\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalDemands }}</div>\r\n            <div class=\"stat-title\">需求对接</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card events\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-tickets\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalEvents }}</div>\r\n            <div class=\"stat-title\">活动报名</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <div class=\"stat-card experts\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n          <div class=\"stat-info\">\r\n            <div class=\"stat-value\">{{ totalExperts }}</div>\r\n            <div class=\"stat-title\">专家库</div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 图表展示区域 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <!-- 业务数据分布饼图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-pie-chart\"></i> 业务数据分布</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"businessChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 用户活跃度趋势图 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"12\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-data-line\"></i> 用户活跃度趋势</span>\r\n          </div>\r\n          <div class=\"chart-container\">\r\n            <div ref=\"userTrendChart\" class=\"chart\"></div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <!-- 最新活动 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-star-on\"></i> 最新活动</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"activity-list\">\r\n            <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"activity-item\">\r\n              <div class=\"activity-title\">{{ activity.title }}</div>\r\n              <div class=\"activity-time\">{{ formatDate(activity.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentActivities.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无活动数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 最新需求 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-connection\"></i> 最新需求</span>\r\n            <el-button type=\"text\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">查看更多</el-button>\r\n          </div>\r\n          <div class=\"demand-list\">\r\n            <div v-for=\"demand in recentDemands\" :key=\"demand.id\" class=\"demand-item\">\r\n              <div class=\"demand-title\">{{ demand.demand_title }}</div>\r\n              <div class=\"demand-time\">{{ formatDate(demand.create_time) }}</div>\r\n            </div>\r\n            <div v-if=\"recentDemands.length === 0\" class=\"empty-data\">\r\n              <i class=\"el-icon-document\"></i>\r\n              <p>暂无需求数据</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 系统状态 -->\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"8\" :lg=\"8\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-monitor\"></i> 系统状态</span>\r\n          </div>\r\n          <div class=\"system-status\">\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon online\">\r\n                <i class=\"el-icon-success\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">系统状态</div>\r\n                <div class=\"status-value\">运行正常</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-time\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">运行时间</div>\r\n                <div class=\"status-value\">{{ getUptime() }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-item\">\r\n              <div class=\"status-icon\">\r\n                <i class=\"el-icon-view\"></i>\r\n              </div>\r\n              <div class=\"status-info\">\r\n                <div class=\"status-label\">活跃用户</div>\r\n                <div class=\"status-value\">{{ activeUsers }} 人</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 快捷操作 -->\r\n    <el-row :gutter=\"20\" class=\"main-content\">\r\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"24\">\r\n        <el-card class=\"content-card\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span><i class=\"el-icon-s-operation\"></i> 快捷操作</span>\r\n          </div>\r\n          <div class=\"quick-actions\">\r\n            <div class=\"action-group\">\r\n              <h4>内容管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/activity')\">\r\n                  新增活动\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/banner')\">\r\n                  新增轮播图\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/notice')\">\r\n                  新增通知\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/news')\">\r\n                  新增新闻\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>用户服务</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/demand')\">\r\n                  发布需求\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/event')\">\r\n                  创建活动\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/job')\">\r\n                  发布职位\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-plus\" size=\"small\" @click=\"goToPage('/miniapp/expert')\">\r\n                  添加专家\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"action-group\">\r\n              <h4>系统管理</h4>\r\n              <div class=\"action-buttons\">\r\n                <el-button type=\"primary\" icon=\"el-icon-user\" size=\"small\" @click=\"goToPage('/system/user')\">\r\n                  用户管理\r\n                </el-button>\r\n                <el-button type=\"success\" icon=\"el-icon-setting\" size=\"small\" @click=\"goToPage('/system/menu')\">\r\n                  菜单管理\r\n                </el-button>\r\n                <el-button type=\"info\" icon=\"el-icon-view\" size=\"small\" @click=\"goToPage('/monitor/operlog')\">\r\n                  操作日志\r\n                </el-button>\r\n                <el-button type=\"warning\" icon=\"el-icon-monitor\" size=\"small\" @click=\"goToPage('/monitor/server')\">\r\n                  服务监控\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: \"Index\",\r\n  data() {\r\n    return {\r\n      // 版本号\r\n      version: \"3.9.0\",\r\n      // 统计数据\r\n      totalUsers: 0,\r\n      activeUsers: 0,\r\n      totalActivities: 0,\r\n      totalDemands: 0,\r\n      totalEvents: 0,\r\n      totalEnterprises: 0,\r\n      totalJobs: 0,\r\n      totalExperts: 0,\r\n      totalNews: 0,\r\n      todayOperations: 0,\r\n      // 最新数据\r\n      recentActivities: [],\r\n      recentDemands: [],\r\n      // 图表实例\r\n      businessChart: null,\r\n      userTrendChart: null\r\n    }\r\n  },\r\n  created() {\r\n    this.loadDashboardData()\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initCharts()\r\n      // 监听窗口大小变化\r\n      window.addEventListener('resize', this.handleResize)\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁图表实例\r\n    if (this.businessChart) {\r\n      this.businessChart.dispose()\r\n    }\r\n    if (this.userTrendChart) {\r\n      this.userTrendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载仪表盘数据\r\n    async loadDashboardData() {\r\n      try {\r\n        // 获取统计数据\r\n        await this.loadStatistics()\r\n        // 获取最新活动\r\n        await this.loadRecentActivities()\r\n        // 获取最新需求\r\n        await this.loadRecentDemands()\r\n      } catch (error) {\r\n        console.error('加载仪表盘数据失败:', error)\r\n        // 使用默认数据\r\n        this.setDefaultData()\r\n      }\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      // 这里可以调用后端API获取统计数据\r\n      // 暂时使用从数据库查询到的真实数据\r\n      this.totalUsers = 6\r\n      this.activeUsers = 4 // 活跃用户数（最近7天登录的用户）\r\n      this.totalActivities = 2\r\n      this.totalDemands = 2\r\n      this.totalEvents = 4\r\n      this.totalEnterprises = 2\r\n      this.totalJobs = 8\r\n      this.totalExperts = 4\r\n      this.totalNews = 2\r\n      this.todayOperations = 36\r\n    },\r\n\r\n    // 加载最新活动\r\n    async loadRecentActivities() {\r\n      // 这里可以调用后端API获取最新活动\r\n      this.recentActivities = [\r\n        { id: 1, title: \"测试页\", create_time: \"2025-07-18T01:39:58.000Z\" },\r\n        { id: 2, title: \"江西宇悦科技有限公司\", create_time: \"2025-07-16T03:14:25.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 加载最新需求\r\n    async loadRecentDemands() {\r\n      // 这里可以调用后端API获取最新需求\r\n      this.recentDemands = [\r\n        { id: 1, demand_title: \"寻球寻求寻求\", create_time: \"2025-07-30T07:43:00.000Z\" },\r\n        { id: 2, demand_title: \"我是夏宇杰\", create_time: \"2025-07-30T06:55:45.000Z\" }\r\n      ]\r\n    },\r\n\r\n    // 设置默认数据\r\n    setDefaultData() {\r\n      this.totalUsers = 0\r\n      this.activeUsers = 0\r\n      this.totalActivities = 0\r\n      this.totalDemands = 0\r\n      this.totalEvents = 0\r\n      this.totalEnterprises = 0\r\n      this.totalJobs = 0\r\n      this.totalExperts = 0\r\n      this.totalNews = 0\r\n      this.todayOperations = 0\r\n      this.recentActivities = []\r\n      this.recentDemands = []\r\n    },\r\n\r\n    // 初始化图表\r\n    initCharts() {\r\n      this.initBusinessChart()\r\n      this.initUserTrendChart()\r\n    },\r\n\r\n    // 初始化业务数据分布饼图\r\n    initBusinessChart() {\r\n      if (!this.$refs.businessChart) return\r\n\r\n      this.businessChart = echarts.init(this.$refs.businessChart)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '业务模块分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '业务数据',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['50%', '45%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: 10,\r\n              borderColor: '#fff',\r\n              borderWidth: 2\r\n            },\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '16',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              { value: this.totalActivities, name: '精彩活动', itemStyle: { color: '#409eff' } },\r\n              { value: this.totalDemands, name: '需求对接', itemStyle: { color: '#67c23a' } },\r\n              { value: this.totalEvents, name: '活动报名', itemStyle: { color: '#e6a23c' } },\r\n              { value: this.totalExperts, name: '专家库', itemStyle: { color: '#f56c6c' } },\r\n              { value: this.totalJobs, name: '招聘职位', itemStyle: { color: '#909399' } },\r\n              { value: this.totalNews, name: '新闻资讯', itemStyle: { color: '#606266' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.businessChart.setOption(option)\r\n    },\r\n\r\n    // 初始化用户活跃度趋势图\r\n    initUserTrendChart() {\r\n      if (!this.$refs.userTrendChart) return\r\n\r\n      this.userTrendChart = echarts.init(this.$refs.userTrendChart)\r\n\r\n      // 模拟最近7天的数据\r\n      const dates = []\r\n      const activeData = []\r\n      const totalData = []\r\n\r\n      for (let i = 6; i >= 0; i--) {\r\n        const date = new Date()\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))\r\n        activeData.push(Math.floor(Math.random() * 5) + 2) // 2-6之间的随机数\r\n        totalData.push(Math.floor(Math.random() * 3) + 6) // 6-8之间的随机数\r\n      }\r\n\r\n      const option = {\r\n        title: {\r\n          text: '用户活跃度（最近7天）',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis'\r\n        },\r\n        legend: {\r\n          bottom: '5%',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '15%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 11\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '活跃用户',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#409eff'\r\n            },\r\n            itemStyle: {\r\n              color: '#409eff'\r\n            },\r\n            areaStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'\r\n                }, {\r\n                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'\r\n                }]\r\n              }\r\n            },\r\n            data: activeData\r\n          },\r\n          {\r\n            name: '总用户',\r\n            type: 'line',\r\n            smooth: true,\r\n            lineStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            itemStyle: {\r\n              color: '#67c23a'\r\n            },\r\n            data: totalData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.userTrendChart.setOption(option)\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      if (this.businessChart) {\r\n        this.businessChart.resize()\r\n      }\r\n      if (this.userTrendChart) {\r\n        this.userTrendChart.resize()\r\n      }\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 获取系统运行时间\r\n    getUptime() {\r\n      const now = new Date()\r\n      const startTime = new Date('2025-01-01') // 假设系统启动时间\r\n      const diff = now - startTime\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      return `${days} 天`\r\n    },\r\n\r\n    // 页面跳转\r\n    goToPage(path) {\r\n      this.$router.push(path)\r\n    },\r\n\r\n    // 外部链接跳转\r\n    goTarget(href) {\r\n      window.open(href, \"_blank\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  // 欢迎横幅样式\r\n  .welcome-banner {\r\n    background: #409eff;\r\n    border-radius: 8px;\r\n    padding: 30px;\r\n    margin-bottom: 20px;\r\n    color: white;\r\n    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);\r\n\r\n    .banner-title {\r\n      font-size: 28px;\r\n      font-weight: 600;\r\n      margin: 0 0 8px 0;\r\n\r\n      i {\r\n        color: #ffd700;\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .banner-subtitle {\r\n      font-size: 14px;\r\n      opacity: 0.9;\r\n      margin: 0 0 25px 0;\r\n    }\r\n\r\n    .banner-stats {\r\n      display: flex;\r\n      gap: 30px;\r\n      flex-wrap: wrap;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .stat-number {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 12px;\r\n          opacity: 0.8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 统计卡片样式\r\n  .stats-row {\r\n    margin-bottom: 20px;\r\n\r\n    .stat-card {\r\n      background: white;\r\n      border-radius: 8px;\r\n      padding: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      transition: all 0.3s ease;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n      }\r\n\r\n      .stat-icon {\r\n        width: 60px;\r\n        height: 60px;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 15px;\r\n\r\n        i {\r\n          font-size: 24px;\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-info {\r\n        flex: 1;\r\n\r\n        .stat-value {\r\n          font-size: 24px;\r\n          font-weight: 700;\r\n          line-height: 1;\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .stat-title {\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n      }\r\n\r\n      &.activities {\r\n        .stat-icon {\r\n          background: #409eff;\r\n        }\r\n        .stat-value {\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      &.demands {\r\n        .stat-icon {\r\n          background: #67c23a;\r\n        }\r\n        .stat-value {\r\n          color: #67c23a;\r\n        }\r\n      }\r\n\r\n      &.events {\r\n        .stat-icon {\r\n          background: #e6a23c;\r\n        }\r\n        .stat-value {\r\n          color: #e6a23c;\r\n        }\r\n      }\r\n\r\n      &.experts {\r\n        .stat-icon {\r\n          background: #f56c6c;\r\n        }\r\n        .stat-value {\r\n          color: #f56c6c;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 图表展示区域样式\r\n  .charts-row {\r\n    margin-bottom: 20px;\r\n\r\n    .chart-container {\r\n      padding: 10px 0;\r\n\r\n      .chart {\r\n        width: 100%;\r\n        height: 300px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域样式\r\n  .main-content {\r\n    margin-bottom: 20px;\r\n\r\n    .content-card {\r\n      height: 100%;\r\n      box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n      border-radius: 8px;\r\n\r\n      .card-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        font-weight: 600;\r\n\r\n        i {\r\n          margin-right: 8px;\r\n          color: #409eff;\r\n        }\r\n      }\r\n\r\n      // 活动列表样式\r\n      .activity-list, .demand-list {\r\n        .activity-item, .demand-item {\r\n          padding: 12px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .activity-title, .demand-title {\r\n            font-size: 14px;\r\n            color: #333;\r\n            margin-bottom: 5px;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n\r\n          .activity-time, .demand-time {\r\n            font-size: 12px;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 系统状态样式\r\n      .system-status {\r\n        .status-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 15px 0;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .status-icon {\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 8px;\r\n            background: #f5f7fa;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 15px;\r\n\r\n            i {\r\n              font-size: 18px;\r\n              color: #909399;\r\n            }\r\n\r\n            &.online i {\r\n              color: #67c23a;\r\n            }\r\n          }\r\n\r\n          .status-info {\r\n            flex: 1;\r\n\r\n            .status-label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 4px;\r\n            }\r\n\r\n            .status-value {\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 1;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 空数据样式\r\n      .empty-data {\r\n        text-align: center;\r\n        padding: 40px 0;\r\n        color: #999;\r\n\r\n        i {\r\n          font-size: 48px;\r\n          margin-bottom: 10px;\r\n          display: block;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 快捷操作样式\r\n  .quick-actions {\r\n    .action-group {\r\n      margin-bottom: 30px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      h4 {\r\n        margin: 0 0 15px 0;\r\n        font-size: 16px;\r\n        color: #333;\r\n        font-weight: 600;\r\n        border-left: 4px solid #409eff;\r\n        padding-left: 10px;\r\n      }\r\n\r\n      .action-buttons {\r\n        display: flex;\r\n        gap: 10px;\r\n        flex-wrap: wrap;\r\n\r\n        .el-button {\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    padding: 10px;\r\n\r\n    .welcome-banner {\r\n      padding: 20px;\r\n\r\n      .banner-title {\r\n        font-size: 22px;\r\n      }\r\n\r\n      .banner-stats {\r\n        gap: 20px;\r\n\r\n        .stat-item {\r\n          .stat-number {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .stats-row {\r\n      .stat-card {\r\n        padding: 15px;\r\n\r\n        .stat-icon {\r\n          width: 50px;\r\n          height: 50px;\r\n\r\n          i {\r\n            font-size: 20px;\r\n          }\r\n        }\r\n\r\n        .stat-info {\r\n          .stat-value {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .charts-row {\r\n      .chart-container {\r\n        .chart {\r\n          height: 250px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .quick-actions {\r\n      .action-group {\r\n        .action-buttons {\r\n          .el-button {\r\n            font-size: 12px;\r\n            padding: 6px 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"]}]}