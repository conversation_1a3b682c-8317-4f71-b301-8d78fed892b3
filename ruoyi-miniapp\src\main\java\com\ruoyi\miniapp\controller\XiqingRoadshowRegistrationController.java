package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.XiqingRoadshowRegistration;
import com.ruoyi.miniapp.service.IXiqingRoadshowRegistrationService;
import com.ruoyi.miniapp.service.IXiqingRoadshowActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 西青金种子路演报名管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "西青金种子-路演报名管理")
@RestController
@RequestMapping("/miniapp/xiqing/registration-manage")
public class XiqingRoadshowRegistrationController extends BaseController
{
    @Autowired
    private IXiqingRoadshowRegistrationService xiqingRoadshowRegistrationService;
    
    @Autowired
    private IXiqingRoadshowActivityService xiqingRoadshowActivityService;

    /**
     * 查询西青金种子路演报名管理列表
     */
    @ApiOperation("查询路演报名列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        startPage();
        List<XiqingRoadshowRegistration> list = xiqingRoadshowRegistrationService.selectXiqingRoadshowRegistrationList(xiqingRoadshowRegistration);
        return getDataTable(list);
    }

    /**
     * 导出西青金种子路演报名管理列表
     */
    @ApiOperation("导出路演报名列表")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:registration:export')")
    @Log(title = "西青金种子路演报名管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        List<XiqingRoadshowRegistration> list = xiqingRoadshowRegistrationService.selectXiqingRoadshowRegistrationList(xiqingRoadshowRegistration);
        ExcelUtil<XiqingRoadshowRegistration> util = new ExcelUtil<XiqingRoadshowRegistration>(XiqingRoadshowRegistration.class);
        util.exportExcel(response, list, "西青金种子路演报名管理数据");
    }

    /**
     * 获取西青金种子路演报名管理详细信息
     */
    @ApiOperation("获取路演报名详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:registration:query')")
    @GetMapping(value = "/{registrationId}")
    public AjaxResult getInfo(@ApiParam("报名ID") @PathVariable("registrationId") Long registrationId)
    {
        return AjaxResult.success(xiqingRoadshowRegistrationService.selectXiqingRoadshowRegistrationByRegistrationId(registrationId));
    }

    /**
     * 删除西青金种子路演报名管理
     */
    @ApiOperation("删除路演报名")
    @PreAuthorize("@ss.hasPermi('miniapp:xiqing:registration:remove')")
    @Log(title = "西青金种子路演报名管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@ApiParam("报名ID数组") @PathVariable Long[] registrationIds)
    {
        return toAjax(xiqingRoadshowRegistrationService.deleteXiqingRoadshowRegistrationByRegistrationIds(registrationIds));
    }



    // ==================== 小程序端接口 ====================

    /**
     * 提交路演报名（小程序端）
     */
    @Anonymous
    @ApiOperation("提交路演报名")
    @PostMapping("/app/register")
    public AjaxResult register(@ApiParam("报名信息") @RequestBody XiqingRoadshowRegistration xiqingRoadshowRegistration)
    {
        // 检查活动是否存在且启用
        if (xiqingRoadshowRegistration.getActivityId() == null) {
            return AjaxResult.error("活动ID不能为空");
        }

        if (xiqingRoadshowRegistration.getUserId() == null) {
            return AjaxResult.error("用户ID不能为空");
        }

        // 检查是否重复提交
        XiqingRoadshowRegistration queryParam = new XiqingRoadshowRegistration();
        queryParam.setActivityId(xiqingRoadshowRegistration.getActivityId());
        queryParam.setUserId(xiqingRoadshowRegistration.getUserId());
        List<XiqingRoadshowRegistration> existingRegistrations = xiqingRoadshowRegistrationService.selectXiqingRoadshowRegistrationList(queryParam);

        if (!existingRegistrations.isEmpty()) {
            return AjaxResult.success("报名成功，请勿重复提交报名");
        }

        // 设置报名信息
        xiqingRoadshowRegistration.setRegistrationTime(DateUtils.getNowDate());

        int result = xiqingRoadshowRegistrationService.insertXiqingRoadshowRegistration(xiqingRoadshowRegistration);
        if (result > 0) {
            // 更新活动报名人数
            xiqingRoadshowActivityService.incrementParticipantCount(xiqingRoadshowRegistration.getActivityId());
            return AjaxResult.success("报名成功");
        }
        return AjaxResult.error("报名失败");
    }
}
