{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955784453}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}