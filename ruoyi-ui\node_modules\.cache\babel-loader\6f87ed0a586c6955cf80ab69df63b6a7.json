{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImagePreview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImagePreview\\index.vue", "mtime": 1754030579760}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_validate", "require", "name", "props", "src", "type", "String", "default", "width", "Number", "height", "computed", "realSrc", "real_src", "split", "isExternal", "process", "env", "VUE_APP_BASE_API", "realSrcList", "real_src_list", "srcList", "for<PERSON>ach", "item", "push", "realWidth", "concat", "realHeight"], "sources": ["src/components/ImagePreview/index.vue"], "sourcesContent": ["<template>\r\n  <el-image\r\n    :src=\"`${realSrc}`\"\r\n    fit=\"cover\"\r\n    :style=\"`width:${realWidth};height:${realHeight};`\"\r\n    :preview-src-list=\"realSrcList\"\r\n  >\r\n    <div slot=\"error\" class=\"image-slot\">\r\n      <i class=\"el-icon-picture-outline\"></i>\r\n    </div>\r\n  </el-image>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from \"@/utils/validate\"\r\n\r\nexport default {\r\n  name: \"ImagePreview\",\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    width: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    },\r\n    height: {\r\n      type: [Number, String],\r\n      default: \"\"\r\n    }\r\n  },\r\n  computed: {\r\n    realSrc() {\r\n      if (!this.src) {\r\n        return\r\n      }\r\n      let real_src = this.src.split(\",\")[0]\r\n      if (isExternal(real_src)) {\r\n        return real_src\r\n      }\r\n      return process.env.VUE_APP_BASE_API + real_src\r\n    },\r\n    realSrcList() {\r\n      if (!this.src) {\r\n        return\r\n      }\r\n      let real_src_list = this.src.split(\",\")\r\n      let srcList = []\r\n      real_src_list.forEach(item => {\r\n        if (isExternal(item)) {\r\n          return srcList.push(item)\r\n        }\r\n        return srcList.push(process.env.VUE_APP_BASE_API + item)\r\n      })\r\n      return srcList\r\n    },\r\n    realWidth() {\r\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`\r\n    },\r\n    realHeight() {\r\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-image {\r\n  border-radius: 5px;\r\n  background-color: #ebeef5;\r\n  box-shadow: 0 0 5px 1px #ccc;\r\n  ::v-deep .el-image__inner {\r\n    transition: all 0.3s;\r\n    cursor: pointer;\r\n    &:hover {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  ::v-deep .image-slot {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    height: 100%;\r\n    color: #909399;\r\n    font-size: 30px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAcA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,GAAAI,MAAA,EAAAH,MAAA;MACAC,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,GAAAI,MAAA,EAAAH,MAAA;MACAC,OAAA;IACA;EACA;EACAI,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,UAAAR,GAAA;QACA;MACA;MACA,IAAAS,QAAA,QAAAT,GAAA,CAAAU,KAAA;MACA,QAAAC,oBAAA,EAAAF,QAAA;QACA,OAAAA,QAAA;MACA;MACA,OAAAG,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAL,QAAA;IACA;IACAM,WAAA,WAAAA,YAAA;MACA,UAAAf,GAAA;QACA;MACA;MACA,IAAAgB,aAAA,QAAAhB,GAAA,CAAAU,KAAA;MACA,IAAAO,OAAA;MACAD,aAAA,CAAAE,OAAA,WAAAC,IAAA;QACA,QAAAR,oBAAA,EAAAQ,IAAA;UACA,OAAAF,OAAA,CAAAG,IAAA,CAAAD,IAAA;QACA;QACA,OAAAF,OAAA,CAAAG,IAAA,CAAAR,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAK,IAAA;MACA;MACA,OAAAF,OAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,mBAAAjB,KAAA,oBAAAA,KAAA,MAAAkB,MAAA,MAAAlB,KAAA;IACA;IACAmB,UAAA,WAAAA,WAAA;MACA,mBAAAjB,MAAA,oBAAAA,MAAA,MAAAgB,MAAA,MAAAhB,MAAA;IACA;EACA;AACA", "ignoreList": []}]}