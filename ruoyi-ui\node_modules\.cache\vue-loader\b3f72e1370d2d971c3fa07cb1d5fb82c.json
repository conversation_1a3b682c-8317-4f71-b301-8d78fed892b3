{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TWluaVVzZXJBZG1pbiwgZ2V0TWluaVVzZXIsIGNoYW5nZU1pbmlVc2VyU3RhdHVzLCBiYXRjaERpc2FibGVNaW5pVXNlciB9IGZyb20gIkAvYXBpL21pbmlhcHAvdXNlciI7DQppbXBvcnQgeyBnZXROb2Rlc0J5TGV2ZWwsIGdldEJhdGNoSW5kdXN0cnlJbmZvIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9pbmR1c3RyeSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlVc2VyIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJywgJ3N5c191c2VyX3NleCddLA0KICBjb21wb25lbnRzOiB7DQogICAgSW1hZ2VVcGxvYWQ6ICgpID0+IGltcG9ydCgiQC9jb21wb25lbnRzL0ltYWdlVXBsb2FkIiksDQogICAgSW1hZ2VQcmV2aWV3OiAoKSA9PiBpbXBvcnQoIkAvY29tcG9uZW50cy9JbWFnZVByZXZpZXciKQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICAvLyDml6XmnJ/ojIPlm7QNCiAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICAvLyDnlKjmiLfor6bmg4Xlr7nor53moYYNCiAgICAgIG9wZW5WaWV3OiBmYWxzZSwNCiAgICAgIC8vIOeUqOaIt+ivpuaDheaVsOaNrg0KICAgICAgdmlld0Zvcm06IHt9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHNlYXJjaFZhbHVlOiB1bmRlZmluZWQsDQogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHdlaXhpbk5pY2tuYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHJlYWxOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICBncmFkdWF0aW9uWWVhcjogdW5kZWZpbmVkLA0KICAgICAgICByZWdpb246IHVuZGVmaW5lZCwNCiAgICAgICAgaW5kdXN0cnlGaWVsZDogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgLy8g5q+V5Lia5bm05Lu96YCJ6aG5DQogICAgICBncmFkdWF0aW9uWWVhcnM6IFtdLA0KICAgICAgLy8g55yB5Lu96YCJ6aG5DQogICAgICBwcm92aW5jZXM6IFsNCiAgICAgICAgJ+WMl+S6rCcsICflpKnmtKUnLCAn5rKz5YyXJywgJ+WxseilvycsICflhoXokpnlj6QnLCAn6L695a6BJywgJ+WQieaelycsICfpu5HpvpnmsZ8nLA0KICAgICAgICAn5LiK5rW3JywgJ+axn+iLjycsICfmtZnmsZ8nLCAn5a6J5b69JywgJ+emj+W7uicsICfmsZ/opb8nLCAn5bGx5LicJywgJ+ays+WNlycsDQogICAgICAgICfmuZbljJcnLCAn5rmW5Y2XJywgJ+W5v+S4nCcsICflub/opb8nLCAn5rW35Y2XJywgJ+mHjeW6hicsICflm5vlt50nLCAn6LS15beeJywNCiAgICAgICAgJ+S6keWNlycsICfopb/ol48nLCAn6ZmV6KW/JywgJ+eUmOiCgycsICfpnZLmtbcnLCAn5a6B5aSPJywgJ+aWsOeWhicsICflj7Dmub4nLA0KICAgICAgICAn6aaZ5rivJywgJ+a+s+mXqCcNCiAgICAgIF0sDQogICAgICAvLyDkuIDnuqfooYzkuJrpgInpobkNCiAgICAgIGZpcnN0TGV2ZWxJbmR1c3RyaWVzOiBbXSwNCiAgICAgIC8vIOWIl+S/oeaBrw0KICAgICAgY29sdW1uczogWw0KICAgICAgICB7IGtleTogMCwgbGFiZWw6IGDnlKjmiLfnvJblj7dgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAxLCBsYWJlbDogYOW+ruS/oeaYteensGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDIsIGxhYmVsOiBg5b6u5L+h5aS05YOPYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMywgbGFiZWw6IGDlp5PlkI1gLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA0LCBsYWJlbDogYOaJi+acuuWPt+eggWAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDUsIGxhYmVsOiBg5b2i6LGh54WnYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogNiwgbGFiZWw6IGDmr5XkuJrpmaLmoKFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA3LCBsYWJlbDogYOaJgOWxnuS8geS4mmAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDgsIGxhYmVsOiBg6KGM5Lia6aKG5Z+fYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogOSwgbGFiZWw6IGDnirbmgIFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAxMCwgbGFiZWw6IGDliJvlu7rml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0NCiAgICAgIF0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHdlaXhpbk5pY2tuYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW+ruS/oeaYteensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiAxLCBtYXg6IDMwLCBtZXNzYWdlOiAi5b6u5L+h5pi156ew6ZW/5bqm5b+F6aG75ZyoMeWIsDMw5Liq5a2X56ym5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcmVhbE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtaW46IDIsIG1heDogMzAsIG1lc3NhZ2U6ICLlp5PlkI3plb/luqblv4XpobvlnKgy5YiwMzDkuKrlrZfnrKbkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwaG9uZW51bWJlcjogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszLTldXGR7OX0kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmluaXRHcmFkdWF0aW9uWWVhcnMoKTsNCiAgICB0aGlzLmluaXRGaXJzdExldmVsSW5kdXN0cmllcygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgLy8g5ZCO5Y+w566h55CG5L2/55So5paw55qEIEFQSe+8jOafpeivouaJgOacieeUqOaIt++8iOWMheaLrOWBnOeUqOeahO+8iQ0KICAgICAgbGlzdE1pbmlVc2VyQWRtaW4odGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOeUqOaIt+eKtuaAgeS/ruaUuQ0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIC8vIOS9v+eUqOecn+WunuWnk+WQjeaIluW+ruS/oeaYteensOS9nOS4uuaYvuekuuWQjeensA0KICAgICAgbGV0IGRpc3BsYXlOYW1lID0gcm93LnJlYWxOYW1lIHx8IHJvdy53ZWl4aW5OaWNrbmFtZSB8fCByb3cudXNlck5hbWUgfHwgJ+ivpeeUqOaItyc7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgZGlzcGxheU5hbWUgKyAnIueUqOaIt+WQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VNaW5pVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKu5ZKM6KGo5Y2V6YeN572u5pa55rOV5bey56e76Zmk77ya5LiN5YaN6ZyA6KaB5L+u5pS55Yqf6IO9DQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS51c2VySWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCg0KDQogICAgLyoqIOafpeeci+ivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGFzeW5jIGhhbmRsZVZpZXcocm93KSB7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkOw0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNaW5pVXNlcih1c2VySWQpOw0KICAgICAgICB0aGlzLnZpZXdGb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g6Kej5p6Q6KGM5Lia5qCH562+DQogICAgICAgIGF3YWl0IHRoaXMucGFyc2VJbmR1c3RyeVRhZ3ModGhpcy52aWV3Rm9ybSk7DQogICAgICAgIHRoaXMub3BlblZpZXcgPSB0cnVlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi36K+m5oOF5aSx6LSlJywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6I635Y+W55So5oi36K+m5oOF5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6Kej5p6Q6KGM5Lia5qCH562+ICovDQogICAgYXN5bmMgcGFyc2VJbmR1c3RyeVRhZ3ModXNlcikgew0KICAgICAgaWYgKCF1c2VyLmluZHVzdHJ5RmllbGQpIHsNCiAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBpbmR1c3RyeUlkcyA9IHVzZXIuaW5kdXN0cnlGaWVsZC5zcGxpdCgnLCcpDQogICAgICAgICAgLmZpbHRlcihpZCA9PiBpZC50cmltKCkpDQogICAgICAgICAgLm1hcChpZCA9PiBwYXJzZUludChpZC50cmltKCkpKQ0KICAgICAgICAgIC5maWx0ZXIoaWQgPT4gIWlzTmFOKGlkKSk7DQoNCiAgICAgICAgaWYgKGluZHVzdHJ5SWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHVzZXIuaW5kdXN0cnlUYWdzID0gW107DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5om56YeP5p+l6K+i6KGM5Lia5L+h5oGvDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QmF0Y2hJbmR1c3RyeUluZm8oaW5kdXN0cnlJZHMpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgICBpZDogaXRlbS5pZCwNCiAgICAgICAgICAgIG5vZGVOYW1lOiBpdGVtLm5vZGVOYW1lLA0KICAgICAgICAgICAgbm9kZVR5cGU6IGl0ZW0ubm9kZVR5cGUsDQogICAgICAgICAgICBub2RlTGV2ZWw6IGl0ZW0ubm9kZUxldmVsLA0KICAgICAgICAgICAgc3RyZWFtVHlwZTogaXRlbS5zdHJlYW1UeXBlLA0KICAgICAgICAgICAgcm9vdE5vZGU6IGl0ZW0ucm9vdE5vZGUNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGM5Lia5qCH562+5aSx6LSlJywgZXJyb3IpOw0KICAgICAgICB1c2VyLmluZHVzdHJ5VGFncyA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOino+aekOe8lui+keihqOWNleS4reeahOihjOS4muagh+etviAtIOW3suemgeeUqCAqLw0KICAgIC8qDQogICAgYXN5bmMgcGFyc2VGb3JtSW5kdXN0cnlUYWdzKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uaW5kdXN0cnlGaWVsZCkgew0KICAgICAgICB0aGlzLmZvcm0uaW5kdXN0cnlUYWdzID0gW107DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgaW5kdXN0cnlJZHMgPSB0aGlzLmZvcm0uaW5kdXN0cnlGaWVsZC5zcGxpdCgnLCcpLmZpbHRlcihpZCA9PiBpZC50cmltKCkpOw0KICAgICAgICBjb25zdCBpbmR1c3RyeVRhZ3MgPSBbXTsNCg0KICAgICAgICBmb3IgKGNvbnN0IGluZHVzdHJ5SWQgb2YgaW5kdXN0cnlJZHMpIHsNCiAgICAgICAgICBpZiAoaW5kdXN0cnlJZC50cmltKCkpIHsNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0SW5kdXN0cnlOb2RlSW5mbyhpbmR1c3RyeUlkLnRyaW0oKSk7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgICAgICAgaW5kdXN0cnlUYWdzLnB1c2goew0KICAgICAgICAgICAgICAgICAgaWQ6IHJlc3BvbnNlLmRhdGEuaWQsDQogICAgICAgICAgICAgICAgICBub2RlTmFtZTogcmVzcG9uc2UuZGF0YS5ub2RlTmFtZSwNCiAgICAgICAgICAgICAgICAgIG5vZGVUeXBlOiByZXNwb25zZS5kYXRhLm5vZGVUeXBlLA0KICAgICAgICAgICAgICAgICAgbm9kZUxldmVsOiByZXNwb25zZS5kYXRhLm5vZGVMZXZlbA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYOiOt+WPluihjOS4muS/oeaBr+Wksei0pe+8jElEOiAke2luZHVzdHJ5SWR9YCwgZXJyb3IpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuZm9ybS5pbmR1c3RyeVRhZ3MgPSBpbmR1c3RyeVRhZ3M7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDnvJbovpHooajljZXooYzkuJrmoIfnrb7lpLHotKUnLCBlcnJvcik7DQogICAgICAgIHRoaXMuZm9ybS5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgICovDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAtIOW3suemgeeUqCAqLw0KICAgIC8qDQogICAgYXN5bmMgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TWluaVVzZXIodXNlcklkKTsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g6Kej5p6Q6KGM5Lia5qCH562+DQogICAgICAgIGF3YWl0IHRoaXMucGFyc2VGb3JtSW5kdXN0cnlUYWdzKCk7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS555So5oi3IjsNCiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gIiI7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKUnLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgICovDQoNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuIC0g5bey56aB55SoICovDQogICAgLyoNCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0udXNlcklkICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgdXBkYXRlTWluaVVzZXIodGhpcy5mb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAqLw0KICAgIC8qKiDlgZznlKjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEaXNhYmxlKHJvdykgew0KICAgICAgbGV0IGNvbmZpcm1NZXNzYWdlOw0KICAgICAgbGV0IHVzZXJJZHMgPSBbXTsNCg0KICAgICAgaWYgKHJvdy51c2VySWQpIHsNCiAgICAgICAgLy8g5Y2V5Liq5YGc55SoDQogICAgICAgIHVzZXJJZHMgPSBbcm93LnVzZXJJZF07DQogICAgICAgIGxldCBkaXNwbGF5TmFtZSA9IHJvdy5yZWFsTmFtZSB8fCByb3cud2VpeGluTmlja25hbWUgfHwgcm93LnVzZXJOYW1lIHx8ICfor6XnlKjmiLcnOw0KICAgICAgICBjb25maXJtTWVzc2FnZSA9ICfmmK/lkKbnoa7orqTlgZznlKjnlKjmiLciJyArIGRpc3BsYXlOYW1lICsgJyLvvJ/lgZznlKjlkI7or6XnlKjmiLflsIbml6Dms5XnmbvlvZXlsI/nqIvluo/jgIInOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5om56YeP5YGc55SoDQogICAgICAgIHVzZXJJZHMgPSB0aGlzLmlkczsNCiAgICAgICAgY29uZmlybU1lc3NhZ2UgPSAn5piv5ZCm56Gu6K6k5YGc55So6YCJ5Lit55qEJyArIHRoaXMuaWRzLmxlbmd0aCArICfkuKrnlKjmiLfvvJ/lgZznlKjlkI7ov5nkupvnlKjmiLflsIbml6Dms5XnmbvlvZXlsI/nqIvluo/jgIInOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGNvbmZpcm1NZXNzYWdlKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6LCD55So5om56YeP5YGc55SoQVBJDQogICAgICAgIHJldHVybiBiYXRjaERpc2FibGVNaW5pVXNlcih1c2VySWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5YGc55So5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC91c2VyL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHVzZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog5Yid5aeL5YyW5q+V5Lia5bm05Lu96YCJ6aG5ICovDQogICAgaW5pdEdyYWR1YXRpb25ZZWFycygpIHsNCiAgICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3Qgc3RhcnRZZWFyID0gMTk4MDsNCiAgICAgIHRoaXMuZ3JhZHVhdGlvblllYXJzID0gW107DQogICAgICBmb3IgKGxldCB5ZWFyID0gY3VycmVudFllYXI7IHllYXIgPj0gc3RhcnRZZWFyOyB5ZWFyLS0pIHsNCiAgICAgICAgdGhpcy5ncmFkdWF0aW9uWWVhcnMucHVzaCh5ZWFyLnRvU3RyaW5nKCkpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOWIneWni+WMluS4gOe6p+ihjOS4mumAiemhuSAqLw0KICAgIGFzeW5jIGluaXRGaXJzdExldmVsSW5kdXN0cmllcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0Tm9kZXNCeUxldmVsKDEpOw0KICAgICAgICB0aGlzLmZpcnN0TGV2ZWxJbmR1c3RyaWVzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS4gOe6p+ihjOS4muWksei0pScsIGVycm9yKTsNCiAgICAgICAgdGhpcy5maXJzdExldmVsSW5kdXN0cmllcyA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOmihOiniOW+ruS/oeWktOWDjyAqLw0KICAgIHByZXZpZXdXZWl4aW5BdmF0YXIoYXZhdGFyVXJsKSB7DQogICAgICBpZiAoYXZhdGFyVXJsKSB7DQogICAgICAgIC8vIOWFiOaYvuekuuWKoOi9veS4reeahOWvueivneahhg0KICAgICAgICBjb25zdCBsb2FkaW5nSHRtbCA9IGANCiAgICAgICAgICA8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBjZW50ZXI7IHBhZGRpbmc6IDIwcHg7Ij4NCiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWxvYWRpbmciIHN0eWxlPSJmb250LXNpemU6IDI0cHg7IGNvbG9yOiAjNDA5RUZGOyI+PC9pPg0KICAgICAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLXRvcDogMTBweDsgY29sb3I6ICM2NjY7Ij7lpLTlg4/liqDovb3kuK0uLi48L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgYDsNCg0KICAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgICAgIHRpdGxlOiAn5b6u5L+h5aS05YOP6aKE6KeIJywNCiAgICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogbG9hZGluZ0h0bWwsDQogICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgICAgc2hvd0NvbmZpcm1CdXR0b246IHRydWUsDQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICflhbPpl60nLA0KICAgICAgICAgIGN1c3RvbUNsYXNzOiAnYXZhdGFyLXByZXZpZXctZGlhbG9nJw0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDpooTliqDovb3lm77niYcNCiAgICAgICAgY29uc3QgaW1nID0gbmV3IEltYWdlKCk7DQogICAgICAgIGltZy5vbmxvYWQgPSAoKSA9PiB7DQogICAgICAgICAgLy8g5Zu+54mH5Yqg6L295oiQ5Yqf5ZCO5pu05paw5a+56K+d5qGG5YaF5a65DQogICAgICAgICAgY29uc3QgaW1nSHRtbCA9IGANCiAgICAgICAgICAgIDxpbWcNCiAgICAgICAgICAgICAgc3JjPSIke2F2YXRhclVybH0iDQogICAgICAgICAgICAgIGFsdD0i5b6u5L+h5aS05YOP6aKE6KeIIg0KICAgICAgICAgICAgICByZWZlcnJlcnBvbGljeT0ibm8tcmVmZXJyZXIiDQogICAgICAgICAgICAgIHN0eWxlPSJtYXgtd2lkdGg6IDEwMCU7IG1heC1oZWlnaHQ6IDQwMHB4OyBvYmplY3QtZml0OiBjb250YWluOyBkaXNwbGF5OiBibG9jazsgbWFyZ2luOiAwIGF1dG87IGJvcmRlci1yYWRpdXM6IDhweDsgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOyINCiAgICAgICAgICAgIC8+DQogICAgICAgICAgYDsNCg0KICAgICAgICAgIC8vIOabtOaWsOWvueivneahhuWGheWuuQ0KICAgICAgICAgIGNvbnN0IG1lc3NhZ2VCb3ggPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuYXZhdGFyLXByZXZpZXctZGlhbG9nIC5lbC1tZXNzYWdlLWJveF9fbWVzc2FnZScpOw0KICAgICAgICAgIGlmIChtZXNzYWdlQm94KSB7DQogICAgICAgICAgICBtZXNzYWdlQm94LmlubmVySFRNTCA9IGltZ0h0bWw7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIGltZy5vbmVycm9yID0gKCkgPT4gew0KICAgICAgICAgIC8vIOWbvueJh+WKoOi9veWksei0pQ0KICAgICAgICAgIGNvbnN0IGVycm9ySHRtbCA9IGANCiAgICAgICAgICAgIDxkaXYgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlcjsgcGFkZGluZzogMjBweDsgY29sb3I6ICNGNTZDNkM7Ij4NCiAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcGljdHVyZS1vdXRsaW5lIiBzdHlsZT0iZm9udC1zaXplOiA0OHB4OyBtYXJnaW4tYm90dG9tOiAxMHB4OyI+PC9pPg0KICAgICAgICAgICAgICA8ZGl2PuWktOWDj+WKoOi9veWksei0pTwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJmb250LXNpemU6IDEycHg7IG1hcmdpbi10b3A6IDVweDsgY29sb3I6ICM5OTk7Ij7or7fmo4Dmn6XnvZHnu5zov57mjqXmiJblm77niYfpk77mjqU8L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIGA7DQoNCiAgICAgICAgICBjb25zdCBtZXNzYWdlQm94ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmF2YXRhci1wcmV2aWV3LWRpYWxvZyAuZWwtbWVzc2FnZS1ib3hfX21lc3NhZ2UnKTsNCiAgICAgICAgICBpZiAobWVzc2FnZUJveCkgew0KICAgICAgICAgICAgbWVzc2FnZUJveC5pbm5lckhUTUwgPSBlcnJvckh0bWw7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIGltZy5zcmMgPSBhdmF0YXJVcmw7DQogICAgICAgIGltZy5yZWZlcnJlclBvbGljeSA9ICJuby1yZWZlcnJlciI7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyXA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!--用户数据-->\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"关键字\" prop=\"searchValue\">\r\n            <el-input\r\n              v-model=\"queryParams.searchValue\"\r\n              placeholder=\"搜索姓名、昵称、手机号等\"\r\n              clearable\r\n              style=\"width: 280px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\"\r\n              placeholder=\"用户名称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"微信昵称\" prop=\"weixinNickname\">\r\n            <el-input\r\n              v-model=\"queryParams.weixinNickname\"\r\n              placeholder=\"微信昵称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"真实姓名\" prop=\"realName\">\r\n            <el-input\r\n              v-model=\"queryParams.realName\"\r\n              placeholder=\"真实姓名\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"手机号码\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"毕业年份\" prop=\"graduationYear\">\r\n            <el-select\r\n              v-model=\"queryParams.graduationYear\"\r\n              placeholder=\"请选择毕业年份\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"year in graduationYears\"\r\n                :key=\"year\"\r\n                :label=\"year + '年'\"\r\n                :value=\"year\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地区\" prop=\"region\">\r\n            <el-select\r\n              v-model=\"queryParams.region\"\r\n              placeholder=\"请选择地区\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"province in provinces\"\r\n                :key=\"province\"\r\n                :label=\"province\"\r\n                :value=\"province\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"行业领域\" prop=\"industryField\">\r\n            <el-select\r\n              v-model=\"queryParams.industryField\"\r\n              placeholder=\"请选择行业领域\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"industry in firstLevelIndustries\"\r\n                :key=\"industry.id\"\r\n                :label=\"industry.nodeName\"\r\n                :value=\"industry.id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 200px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >修改</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-close\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDisable\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >停用</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['miniapp:user:export']\"\r\n            >导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n          <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" width=\"80\" />\r\n          <el-table-column label=\"微信昵称\" align=\"center\" key=\"weixinNickname\" prop=\"weixinNickname\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"微信头像\" align=\"center\" key=\"weixinAvatar\" v-if=\"columns[2].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <img v-if=\"scope.row.weixinAvatar\" :src=\"scope.row.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 40px; height: 40px; border-radius: 50%; object-fit: cover;\" />\r\n              <el-avatar v-else :size=\"40\" icon=\"el-icon-user-solid\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"姓名\" align=\"center\" key=\"realName\" prop=\"realName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n          <el-table-column label=\"形象照\" align=\"center\" key=\"portraitUrl\" v-if=\"columns[5].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <image-preview :src=\"scope.row.portraitUrl\" :width=\"50\" :height=\"50\" v-if=\"scope.row.portraitUrl\"/>\r\n              <el-avatar v-else :size=\"50\" icon=\"el-icon-picture\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"毕业院校\" align=\"center\" key=\"graduateSchool\" prop=\"graduateSchool\" v-if=\"columns[6].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"所属企业\" align=\"center\" key=\"currentCompany\" prop=\"currentCompany\" v-if=\"columns[7].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"行业领域\" align=\"center\" key=\"industryField\" v-if=\"columns[8].visible\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.industryNames\">{{ scope.row.industryNames }}</span>\r\n              <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[9].visible\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[10].visible\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"180\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleView(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:query']\"\r\n              >详情</el-button>\r\n              <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n              >修改</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-close\"\r\n                @click=\"handleDisable(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n                :disabled=\"scope.row.status === '1'\"\r\n              >停用</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n    <!-- 修改用户对话框已移除：用户信息不应由管理员修改 -->\r\n\r\n\r\n    <!-- 用户详情对话框 -->\r\n    <el-dialog title=\"用户详情\" :visible.sync=\"openView\" width=\"1000px\" append-to-body>\r\n      <div class=\"user-detail-container\">\r\n        <!-- 基本信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">基本信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"用户编号\">{{ viewForm.userId }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"姓名\">{{ viewForm.realName || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"微信昵称\">{{ viewForm.weixinNickname || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"手机号码\">{{ viewForm.phonenumber || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性别\">\r\n              <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"viewForm.sex\"/>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"出生日期\">{{ parseTime(viewForm.birthDate, '{y}-{m}-{d}') || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"籍贯\">{{ viewForm.region || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"状态\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"viewForm.status\"/>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 头像信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">头像信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">微信头像</div>\r\n                <div class=\"avatar-content\">\r\n                  <img v-if=\"viewForm.weixinAvatar\" :src=\"viewForm.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer;\" @click=\"previewWeixinAvatar(viewForm.weixinAvatar)\" />\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">形象照</div>\r\n                <div class=\"avatar-content\">\r\n                  <image-preview :src=\"viewForm.portraitUrl\" :width=\"80\" :height=\"80\" v-if=\"viewForm.portraitUrl\"/>\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 教育背景 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">教育背景</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"毕业院校\">{{ viewForm.graduateSchool || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"毕业年份\">{{ viewForm.graduationYear || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"专业\">{{ viewForm.major || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学院\">{{ viewForm.college || '未设置' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 职业信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">职业信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"当前公司\">{{ viewForm.currentCompany || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"行业领域\">\r\n              <div v-if=\"viewForm.industryTags && viewForm.industryTags.length > 0\" class=\"industry-tags\">\r\n                <el-tag\r\n                  v-for=\"tag in viewForm.industryTags\"\r\n                  :key=\"tag.id\"\r\n                  size=\"small\"\r\n                  style=\"margin-right: 5px; margin-bottom: 5px;\"\r\n                >\r\n                  {{ tag.nodeName }}\r\n                </el-tag>\r\n              </div>\r\n              <span v-else>未设置</span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"职位名称\">{{ viewForm.positionTitle || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"个人介绍\" :span=\"2\">\r\n              <div class=\"personal-intro\">\r\n                {{ viewForm.personalIntroduction || '未设置' }}\r\n              </div>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 积分信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">积分信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item label=\"总积分\">{{ viewForm.totalPoints || 0 }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 系统信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">系统信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"最后登录时间\">{{ parseTime(viewForm.lastLoginTime) || '未登录' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"创建时间\">{{ parseTime(viewForm.createTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"更新时间\">{{ parseTime(viewForm.updateTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"备注\" :span=\"2\">{{ viewForm.remark || '无备注' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"openView = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMiniUserAdmin, getMiniUser, changeMiniUserStatus, batchDisableMiniUser } from \"@/api/miniapp/user\";\r\nimport { getNodesByLevel, getBatchIndustryInfo } from \"@/api/miniapp/industry\";\r\n\r\nexport default {\r\n  name: \"MiniUser\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: {\r\n    ImageUpload: () => import(\"@/components/ImageUpload\"),\r\n    ImagePreview: () => import(\"@/components/ImagePreview\")\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 用户详情对话框\r\n      openView: false,\r\n      // 用户详情数据\r\n      viewForm: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        searchValue: undefined,\r\n        userName: undefined,\r\n        weixinNickname: undefined,\r\n        realName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        graduationYear: undefined,\r\n        region: undefined,\r\n        industryField: undefined\r\n      },\r\n      // 毕业年份选项\r\n      graduationYears: [],\r\n      // 省份选项\r\n      provinces: [\r\n        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',\r\n        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',\r\n        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',\r\n        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾',\r\n        '香港', '澳门'\r\n      ],\r\n      // 一级行业选项\r\n      firstLevelIndustries: [],\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `微信昵称`, visible: true },\r\n        { key: 2, label: `微信头像`, visible: true },\r\n        { key: 3, label: `姓名`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `形象照`, visible: true },\r\n        { key: 6, label: `毕业院校`, visible: true },\r\n        { key: 7, label: `所属企业`, visible: true },\r\n        { key: 8, label: `行业领域`, visible: true },\r\n        { key: 9, label: `状态`, visible: true },\r\n        { key: 10, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        weixinNickname: [\r\n          { required: true, message: \"微信昵称不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 30, message: \"微信昵称长度必须在1到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        realName: [\r\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 30, message: \"姓名长度必须在2到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.initGraduationYears();\r\n    this.initFirstLevelIndustries();\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 后台管理使用新的 API，查询所有用户（包括停用的）\r\n      listMiniUserAdmin(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      // 使用真实姓名或微信昵称作为显示名称\r\n      let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + displayName + '\"用户吗？').then(function() {\r\n        return changeMiniUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮和表单重置方法已移除：不再需要修改功能\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n\r\n    /** 查看详情按钮操作 */\r\n    async handleView(row) {\r\n      const userId = row.userId;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.viewForm = response.data;\r\n        // 解析行业标签\r\n        await this.parseIndustryTags(this.viewForm);\r\n        this.openView = true;\r\n      } catch (error) {\r\n        console.error('获取用户详情失败', error);\r\n        this.$modal.msgError('获取用户详情失败');\r\n      }\r\n    },\r\n    /** 解析行业标签 */\r\n    async parseIndustryTags(user) {\r\n      if (!user.industryField) {\r\n        user.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = user.industryField.split(',')\r\n          .filter(id => id.trim())\r\n          .map(id => parseInt(id.trim()))\r\n          .filter(id => !isNaN(id));\r\n\r\n        if (industryIds.length === 0) {\r\n          user.industryTags = [];\r\n          return;\r\n        }\r\n\r\n        // 批量查询行业信息\r\n        const response = await getBatchIndustryInfo(industryIds);\r\n        if (response.data && Array.isArray(response.data)) {\r\n          user.industryTags = response.data.map(item => ({\r\n            id: item.id,\r\n            nodeName: item.nodeName,\r\n            nodeType: item.nodeType,\r\n            nodeLevel: item.nodeLevel,\r\n            streamType: item.streamType,\r\n            rootNode: item.rootNode\r\n          }));\r\n        } else {\r\n          user.industryTags = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('解析行业标签失败', error);\r\n        user.industryTags = [];\r\n      }\r\n    },\r\n    /** 解析编辑表单中的行业标签 - 已禁用 */\r\n    /*\r\n    async parseFormIndustryTags() {\r\n      if (!this.form.industryField) {\r\n        this.form.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = this.form.industryField.split(',').filter(id => id.trim());\r\n        const industryTags = [];\r\n\r\n        for (const industryId of industryIds) {\r\n          if (industryId.trim()) {\r\n            try {\r\n              const response = await getIndustryNodeInfo(industryId.trim());\r\n              if (response.data) {\r\n                industryTags.push({\r\n                  id: response.data.id,\r\n                  nodeName: response.data.nodeName,\r\n                  nodeType: response.data.nodeType,\r\n                  nodeLevel: response.data.nodeLevel\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取行业信息失败，ID: ${industryId}`, error);\r\n            }\r\n          }\r\n        }\r\n\r\n        this.form.industryTags = industryTags;\r\n      } catch (error) {\r\n        console.error('解析编辑表单行业标签失败', error);\r\n        this.form.industryTags = [];\r\n      }\r\n    },\r\n    */\r\n    /** 修改按钮操作 - 已禁用 */\r\n    /*\r\n    async handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.form = response.data;\r\n        // 解析行业标签\r\n        await this.parseFormIndustryTags();\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      } catch (error) {\r\n        console.error('获取用户信息失败', error);\r\n        this.$modal.msgError('获取用户信息失败');\r\n      }\r\n    },\r\n    */\r\n\r\n    /** 提交按钮 - 已禁用 */\r\n    /*\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateMiniUser(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    */\r\n    /** 停用按钮操作 */\r\n    handleDisable(row) {\r\n      let confirmMessage;\r\n      let userIds = [];\r\n\r\n      if (row.userId) {\r\n        // 单个停用\r\n        userIds = [row.userId];\r\n        let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n        confirmMessage = '是否确认停用用户\"' + displayName + '\"？停用后该用户将无法登录小程序。';\r\n      } else {\r\n        // 批量停用\r\n        userIds = this.ids;\r\n        confirmMessage = '是否确认停用选中的' + this.ids.length + '个用户？停用后这些用户将无法登录小程序。';\r\n      }\r\n\r\n      this.$modal.confirm(confirmMessage).then(() => {\r\n        // 调用批量停用API\r\n        return batchDisableMiniUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"停用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 初始化毕业年份选项 */\r\n    initGraduationYears() {\r\n      const currentYear = new Date().getFullYear();\r\n      const startYear = 1980;\r\n      this.graduationYears = [];\r\n      for (let year = currentYear; year >= startYear; year--) {\r\n        this.graduationYears.push(year.toString());\r\n      }\r\n    },\r\n    /** 初始化一级行业选项 */\r\n    async initFirstLevelIndustries() {\r\n      try {\r\n        const response = await getNodesByLevel(1);\r\n        this.firstLevelIndustries = response.data || [];\r\n      } catch (error) {\r\n        console.error('获取一级行业失败', error);\r\n        this.firstLevelIndustries = [];\r\n      }\r\n    },\r\n    /** 预览微信头像 */\r\n    previewWeixinAvatar(avatarUrl) {\r\n      if (avatarUrl) {\r\n        // 先显示加载中的对话框\r\n        const loadingHtml = `\r\n          <div style=\"text-align: center; padding: 20px;\">\r\n            <i class=\"el-icon-loading\" style=\"font-size: 24px; color: #409EFF;\"></i>\r\n            <div style=\"margin-top: 10px; color: #666;\">头像加载中...</div>\r\n          </div>\r\n        `;\r\n\r\n        this.$msgbox({\r\n          title: '微信头像预览',\r\n          dangerouslyUseHTMLString: true,\r\n          message: loadingHtml,\r\n          showCancelButton: false,\r\n          showConfirmButton: true,\r\n          confirmButtonText: '关闭',\r\n          customClass: 'avatar-preview-dialog'\r\n        });\r\n\r\n        // 预加载图片\r\n        const img = new Image();\r\n        img.onload = () => {\r\n          // 图片加载成功后更新对话框内容\r\n          const imgHtml = `\r\n            <img\r\n              src=\"${avatarUrl}\"\r\n              alt=\"微信头像预览\"\r\n              referrerpolicy=\"no-referrer\"\r\n              style=\"max-width: 100%; max-height: 400px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\"\r\n            />\r\n          `;\r\n\r\n          // 更新对话框内容\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = imgHtml;\r\n          }\r\n        };\r\n\r\n        img.onerror = () => {\r\n          // 图片加载失败\r\n          const errorHtml = `\r\n            <div style=\"text-align: center; padding: 20px; color: #F56C6C;\">\r\n              <i class=\"el-icon-picture-outline\" style=\"font-size: 48px; margin-bottom: 10px;\"></i>\r\n              <div>头像加载失败</div>\r\n              <div style=\"font-size: 12px; margin-top: 5px; color: #999;\">请检查网络连接或图片链接</div>\r\n            </div>\r\n          `;\r\n\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = errorHtml;\r\n          }\r\n        };\r\n\r\n        img.src = avatarUrl;\r\n        img.referrerPolicy = \"no-referrer\";\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.detail-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.card-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 16px;\r\n  background: #409EFF;\r\n  border-radius: 2px;\r\n}\r\n\r\n.avatar-item {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.avatar-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.avatar-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.no-avatar {\r\n  color: #C0C4CC;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 2px dashed #E4E7ED;\r\n  border-radius: 50%;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    max-height: 60vh;\r\n  }\r\n\r\n  .avatar-item {\r\n    padding: 15px;\r\n  }\r\n\r\n  .avatar-content {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .no-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .industry-field-container .industry-tags-display {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .industry-tags .el-tag {\r\n    margin-right: 5px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .personal-intro {\r\n    max-width: 100%;\r\n    word-wrap: break-word;\r\n    white-space: pre-wrap;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n</style>\r\n"]}]}