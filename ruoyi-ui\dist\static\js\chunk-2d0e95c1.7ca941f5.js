(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e95c1"],{"8ccc":function(e,t,i){"use strict";i.r(t);var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{label:"项目名称",prop:"projectName"}},[i("el-input",{attrs:{placeholder:"请输入项目名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.projectName,callback:function(t){e.$set(e.queryParams,"projectName",t)},expression:"queryParams.projectName"}})],1),i("el-form-item",{attrs:{label:"城市",prop:"city"}},[i("el-input",{attrs:{placeholder:"请输入城市",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.city,callback:function(t){e.$set(e.queryParams,"city",t)},expression:"queryParams.city"}})],1),i("el-form-item",{attrs:{label:"行业",prop:"industry"}},[i("el-input",{attrs:{placeholder:"请输入行业",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.industry,callback:function(t){e.$set(e.queryParams,"industry",t)},expression:"queryParams.industry"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:project:edit"],expression:"['miniapp:haitang:project:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-picture",size:"mini"},on:{click:e.handleSponsorUpload}},[e._v("赞助商图片")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:project:remove"],expression:"['miniapp:haitang:project:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:project:export"],expression:"['miniapp:haitang:project:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.projectList},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{attrs:{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":!0}}),i("el-table-column",{attrs:{label:"团队规模",align:"center",prop:"teamSize"}}),i("el-table-column",{attrs:{label:"城市",align:"center",prop:"city"}}),i("el-table-column",{attrs:{label:"赛区",align:"center",prop:"competitionArea"}}),i("el-table-column",{attrs:{label:"行业",align:"center",prop:"industry"}}),i("el-table-column",{attrs:{label:"天大校友",align:"center",prop:"isTjuAlumni"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-tag",{attrs:{type:t.row.isTjuAlumni?"success":"info"}},[e._v(" "+e._s(t.row.isTjuAlumni?"是":"否")+" ")])]}}])}),i("el-table-column",{attrs:{label:"有公司",align:"center",prop:"hasCompany"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-tag",{attrs:{type:t.row.hasCompany?"success":"info"}},[e._v(" "+e._s(t.row.hasCompany?"是":"否")+" ")])]}}])}),i("el-table-column",{attrs:{label:"公司名称",align:"center",prop:"companyName","show-overflow-tooltip":!0}}),i("el-table-column",{attrs:{label:"公司Logo",align:"center",prop:"companyLogo",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.companyLogo?i("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:t.row.companyLogo,"preview-src-list":[t.row.companyLogo],fit:"cover"}}):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{label:"联系人",align:"center",prop:"contactName"}}),i("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"contactPhone"}}),i("el-table-column",{attrs:{label:"报名时间",align:"center",prop:"registrationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.registrationTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:project:query"],expression:"['miniapp:haitang:project:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(i){return e.handleView(t.row)}}},[e._v("查看")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:haitang:project:remove"],expression:"['miniapp:haitang:project:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:"赞助商图片管理",visible:e.sponsorOpen,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.sponsorOpen=t}}},[i("el-form",{ref:"sponsorForm",attrs:{model:e.sponsorForm,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"当前赞助商图片"}},[e.sponsorForm.sponsorUnit&&""!==e.sponsorForm.sponsorUnit.trim()?i("div",{staticStyle:{padding:"10px",background:"#f0f9ff",border:"1px solid #b3d8ff","border-radius":"4px","margin-bottom":"10px"}},[i("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a","margin-right":"5px"}}),i("span",{staticStyle:{color:"#409eff"}},[e._v("已设置赞助商图片")])]):i("div",{staticStyle:{padding:"10px",background:"#fdf6ec",border:"1px solid #f5dab1","border-radius":"4px","margin-bottom":"10px"}},[i("i",{staticClass:"el-icon-warning",staticStyle:{color:"#e6a23c","margin-right":"5px"}}),i("span",{staticStyle:{color:"#e6a23c"}},[e._v("暂未设置赞助商图片")])])]),i("el-form-item",{attrs:{label:"上传新图片"}},[i("image-upload",{attrs:{limit:1},model:{value:e.sponsorForm.sponsorUnit,callback:function(t){e.$set(e.sponsorForm,"sponsorUnit",t)},expression:"sponsorForm.sponsorUnit"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitSponsorForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancelSponsor}},[e._v("取 消")])],1)],1),i("el-dialog",{attrs:{title:"项目报名详情",visible:e.viewOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.viewOpen=t}}},[i("el-descriptions",{attrs:{column:2,border:""}},[i("el-descriptions-item",{attrs:{label:"项目名称"}},[e._v(e._s(e.viewForm.projectName))]),i("el-descriptions-item",{attrs:{label:"团队规模"}},[e._v(e._s(e.viewForm.teamSize)+"人")]),i("el-descriptions-item",{attrs:{label:"城市"}},[e._v(e._s(e.viewForm.city))]),i("el-descriptions-item",{attrs:{label:"赛区"}},[e._v(e._s(e.viewForm.competitionArea))]),i("el-descriptions-item",{attrs:{label:"行业"}},[e._v(e._s(e.viewForm.industry))]),i("el-descriptions-item",{attrs:{label:"天大校友"}},[e._v(e._s(e.viewForm.isTjuAlumni?"是":"否"))]),i("el-descriptions-item",{attrs:{label:"项目描述",span:2}},[e._v(e._s(e.viewForm.projectDescription))]),i("el-descriptions-item",{attrs:{label:"是否有公司"}},[e._v(e._s(e.viewForm.hasCompany?"是":"否"))]),e.viewForm.hasCompany?i("el-descriptions-item",{attrs:{label:"公司名称"}},[e._v(e._s(e.viewForm.companyName))]):e._e(),e.viewForm.hasCompany?i("el-descriptions-item",{attrs:{label:"去年营收"}},[e._v(e._s(e.viewForm.lastYearRevenue)+"万元")]):e._e(),e.viewForm.hasCompany?i("el-descriptions-item",{attrs:{label:"项目估值"}},[e._v(e._s(e.viewForm.projectValuation)+"万元")]):e._e(),e.viewForm.hasCompany?i("el-descriptions-item",{attrs:{label:"最新融资轮次"}},[e._v(e._s(e.viewForm.latestFundingRound))]):e._e(),e.viewForm.hasCompany?i("el-descriptions-item",{attrs:{label:"投资机构"}},[e._v(e._s(e.viewForm.investmentInstitution))]):e._e(),e.viewForm.hasCompany&&e.viewForm.companyLogo?i("el-descriptions-item",{attrs:{label:"公司logo"}},[i("el-image",{staticStyle:{width:"100px",height:"60px"},attrs:{src:e.viewForm.companyLogo,"preview-src-list":[e.viewForm.companyLogo],fit:"cover"}})],1):e._e(),e.viewForm.projectBp?i("el-descriptions-item",{attrs:{label:"项目BP"}},[i("el-link",{attrs:{type:"primary",href:e.viewForm.projectBp,target:"_blank",underline:!1}},[i("i",{staticClass:"el-icon-download"}),e._v(" 下载项目BP ")])],1):e._e(),i("el-descriptions-item",{attrs:{label:"推荐人"}},[e._v(e._s(e.viewForm.recommender))]),e.viewForm.sponsorUnit?i("el-descriptions-item",{attrs:{label:"赞助单位"}},[i("el-image",{staticStyle:{width:"120px",height:"60px"},attrs:{src:e.viewForm.sponsorUnit,"preview-src-list":[e.viewForm.sponsorUnit],fit:"contain"}})],1):e._e(),i("el-descriptions-item",{attrs:{label:"联系人姓名"}},[e._v(e._s(e.viewForm.contactName))]),i("el-descriptions-item",{attrs:{label:"联系人电话"}},[e._v(e._s(e.viewForm.contactPhone))]),i("el-descriptions-item",{attrs:{label:"联系人微信"}},[e._v(e._s(e.viewForm.contactWechat))]),i("el-descriptions-item",{attrs:{label:"联系人职位"}},[e._v(e._s(e.viewForm.contactPosition))]),i("el-descriptions-item",{attrs:{label:"报名时间"}},[e._v(e._s(e.parseTime(e.viewForm.registrationTime,"{y}-{m}-{d} {h}:{i}:{s}")))])],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.viewOpen=!1}}},[e._v("关 闭")])],1)],1)],1)},a=[],r=i("5530"),n=(i("d81d"),i("d3b7"),i("0643"),i("a573"),i("b775"));function s(e){return Object(n["a"])({url:"/miniapp/haitang/project/list",method:"get",params:e})}function l(){return Object(n["a"])({url:"/miniapp/haitang/project/config/sponsor",method:"get"})}function c(e){return Object(n["a"])({url:"/miniapp/haitang/project/config/sponsor",method:"put",data:{sponsorUnit:e}})}function p(e){return Object(n["a"])({url:"/miniapp/haitang/project/"+e,method:"delete"})}var m=i("0835"),u={name:"Project",components:{ImageUpload:m["default"]},data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,projectList:[],sponsorOpen:!1,viewOpen:!1,queryParams:{pageNum:1,pageSize:10,projectName:null,city:null,industry:null},sponsorForm:{sponsorUnit:null},viewForm:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.projectList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.multiple=!e.length},handleSponsorUpload:function(){var e=this;l().then((function(t){e.sponsorForm.sponsorUnit=t.data||"",e.sponsorOpen=!0})).catch((function(t){e.sponsorForm.sponsorUnit="",e.sponsorOpen=!0}))},cancelSponsor:function(){this.sponsorOpen=!1},submitSponsorForm:function(){var e=this;c(this.sponsorForm.sponsorUnit).then((function(t){e.$modal.msgSuccess("赞助商图片更新成功"),e.sponsorOpen=!1})).catch((function(t){e.$modal.msgError("更新赞助商图片失败")}))},handleView:function(e){this.viewForm=e,this.viewOpen=!0},handleDelete:function(e){var t=this,i=e.id||this.ids;this.$modal.confirm('是否确认删除项目报名编号为"'+i+'"的数据项？').then((function(){return p(i)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/haitang/project/export",Object(r["a"])({},this.queryParams),"project_".concat((new Date).getTime(),".xlsx"))}}},d=u,h=i("2877"),v=Object(h["a"])(d,o,a,!1,null,null,null);t["default"]=v.exports}}]);