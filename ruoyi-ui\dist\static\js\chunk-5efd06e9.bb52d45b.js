(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5efd06e9"],{2472:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"活动ID",prop:"activityId"}},[a("el-input",{attrs:{placeholder:"请输入活动ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.activityId,callback:function(t){e.$set(e.queryParams,"activityId",t)},expression:"queryParams.activityId"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:remove"],expression:"['miniapp:xiqing:registration:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:export"],expression:"['miniapp:xiqing:registration:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.registrationManageList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"报名ID",align:"center",prop:"registrationId",width:"80"}}),a("el-table-column",{attrs:{label:"活动标题",align:"center",prop:"activityTitle","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"报名时间",align:"center",prop:"registrationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.registrationTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:query"],expression:"['miniapp:xiqing:registration:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("查看")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["miniapp:xiqing:registration:remove"],expression:"['miniapp:xiqing:registration:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"报名详情",visible:e.viewOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.viewOpen=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"报名ID"}},[e._v(e._s(e.form.registrationId))]),a("el-descriptions-item",{attrs:{label:"活动标题"}},[e._v(e._s(e.form.activityTitle))]),a("el-descriptions-item",{attrs:{label:"报名时间"}},[e._v(e._s(e.parseTime(e.form.registrationTime)))])],1),a("div",{staticStyle:{"margin-top":"20px"}},[a("h4",[e._v("报名表单数据：")]),a("el-table",{staticStyle:{"margin-top":"10px"},attrs:{data:e.formDataList,border:""}},[a("el-table-column",{attrs:{prop:"key",label:"字段名",width:"200"}}),a("el-table-column",{attrs:{label:"字段值"},scopedSlots:e._u([{key:"default",fn:function(t){return["textarea"===t.row.type?a("div",{staticClass:"textarea-content"},[e._v(" "+e._s(t.row.value)+" ")]):"radio"===t.row.type||"picker"===t.row.type||"select"===t.row.type?a("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(" "+e._s(t.row.value)+" ")]):"tel"===t.row.type||"phone"===t.row.type?a("span",{staticClass:"phone-number"},[e._v(" "+e._s(t.row.value)+" ")]):"date"===t.row.type?a("el-tag",{attrs:{type:"info",size:"small"}},[e._v(" "+e._s(t.row.value)+" ")]):"checkbox"===t.row.type?a("div",{staticClass:"checkbox-content"},[e._v(" "+e._s(t.row.value)+" ")]):"file"===t.row.type?a("div",{staticClass:"file-content"},[t.row.fileList&&t.row.fileList.length>0?a("div",e._l(t.row.fileList,(function(t,i){return a("div",{key:i,staticClass:"file-item"},[a("el-link",{staticClass:"file-link",attrs:{type:"primary",href:t.url,target:"_blank",download:t.name}},[a("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t.name)+" ")])],1)})),0):a("span",{staticClass:"no-file"},[e._v("未上传文件")])]):a("span",[e._v(e._s(t.row.value))])]}}])})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.viewOpen=!1}}},[e._v("关 闭")])],1)],1)],1)},r=[],n=a("5530"),o=a("53ca"),s=a("c14f"),l=a("1da1"),c=(a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("b64b"),a("d3b7"),a("2ca0"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("b775"));function u(e){return Object(c["a"])({url:"/miniapp/xiqing/registration-manage/list",method:"get",params:e})}function m(e){return Object(c["a"])({url:"/miniapp/xiqing/registration-manage/"+e,method:"get"})}function p(e){return Object(c["a"])({url:"/miniapp/xiqing/registration-manage/"+e,method:"delete"})}var f=a("26dc"),d={name:"XiqingRegistrationManage",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,registrationManageList:[],viewOpen:!1,formDataList:[],queryParams:{pageNum:1,pageSize:10,activityId:null},form:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,u(this.queryParams).then((function(t){e.registrationManageList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.registrationId})),this.multiple=!e.length},handleView:function(e){var t=this,a=e.registrationId;m(a).then(function(){var e=Object(l["a"])(Object(s["a"])().m((function e(a){return Object(s["a"])().w((function(e){while(1)switch(e.n){case 0:return t.form=a.data,e.n=1,t.parseFormData();case 1:t.viewOpen=!0;case 2:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},parseFormData:function(){var e=this;return Object(l["a"])(Object(s["a"])().m((function t(){var a,i,r,n,l;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:if(e.formDataList=[],!e.form.formData){t.n=6;break}if(t.p=1,a=JSON.parse(e.form.formData),!Array.isArray(a)){t.n=2;break}a.forEach((function(t){if(t.name&&void 0!==t.value&&null!==t.value&&""!==t.value){var a={key:t.label||t.name,value:e.formatFieldValue(t.value,t.type),type:t.type};"file"===t.type&&t.value&&(a.fileList=e.parseFileList(t.value)),e.formDataList.push(a)}})),t.n=4;break;case 2:if("object"!==Object(o["a"])(a)){t.n=4;break}return t.n=3,e.getActivityFormConfig();case 3:for(n in i=t.v,r={},i&&i.forEach((function(e){r[e.name]=e.label})),a)void 0!==a[n]&&null!==a[n]&&""!==a[n]&&e.formDataList.push({key:r[n]||n,value:a[n],type:"text"});case 4:t.n=6;break;case 5:t.p=5,l=t.v,console.error("解析表单数据失败:",l);case 6:return t.a(2)}}),t,null,[[1,5]])})))()},formatFieldValue:function(e,t){if(void 0===e||null===e||""===e)return"未填写";switch(t){case"checkbox":return Array.isArray(e)?e.length>0?e.join(", "):"未选择":e;case"radio":case"picker":case"select":return e||"未选择";case"textarea":return e;case"date":return e||"未选择";case"tel":case"phone":return e;case"file":return e;default:return e}},parseFileList:function(e){var t=this;if(!e)return[];try{if("string"===typeof e){if(!e.startsWith("[")&&!e.startsWith("{"))return[{name:this.getFileNameFromUrl(e),url:e}];var a=JSON.parse(e);if(Array.isArray(a))return a.map((function(e){return{name:e.name||e.fileName||"未知文件",url:e.url||e.path||e}}));if(a.url||a.path)return[{name:a.name||a.fileName||"未知文件",url:a.url||a.path}]}else{if(Array.isArray(e))return e.map((function(e){return"string"===typeof e?{name:t.getFileNameFromUrl(e),url:e}:{name:e.name||e.fileName||"未知文件",url:e.url||e.path||e}}));if("object"===Object(o["a"])(e))return[{name:e.name||e.fileName||"未知文件",url:e.url||e.path||e}]}}catch(i){console.error("解析文件列表失败:",i)}return[]},getFileNameFromUrl:function(e){if(!e)return"未知文件";var t=e.split("/"),a=t[t.length-1];return a||"未知文件"},getActivityFormConfig:function(){var e=this;return Object(l["a"])(Object(s["a"])().m((function t(){var a,i;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:if(e.form.activityId){t.n=1;break}return t.a(2,null);case 1:return t.p=1,t.n=2,Object(f["a"])(e.form.activityId);case 2:if(a=t.v,!a.data||!a.data.formConfig){t.n=3;break}return t.a(2,JSON.parse(a.data.formConfig));case 3:t.n=5;break;case 4:t.p=4,i=t.v,console.error("获取活动表单配置失败:",i);case 5:return t.a(2,null)}}),t,null,[[1,4]])})))()},handleDelete:function(e){var t=this,a=e.registrationId||this.ids;this.$modal.confirm('是否确认删除报名编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/xiqing/registration-manage/export",Object(n["a"])({},this.queryParams),"registration_manage_".concat((new Date).getTime(),".xlsx"))}}},h=d,g=(a("f032"),a("2877")),v=Object(g["a"])(h,i,r,!1,null,"0eba58e9",null);t["default"]=v.exports},"26dc":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var i=a("b775");function r(e){return Object(i["a"])({url:"/miniapp/xiqing/activity-config/"+e,method:"get"})}function n(e){return Object(i["a"])({url:"/miniapp/xiqing/activity-config",method:"put",data:e})}},"67de":function(e,t,a){},f032:function(e,t,a){"use strict";a("67de")}}]);