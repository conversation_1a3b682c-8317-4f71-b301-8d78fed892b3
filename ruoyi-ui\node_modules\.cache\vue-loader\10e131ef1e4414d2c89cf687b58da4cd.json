{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1754037225576}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlLCBnZXRSZWdpc3RyYXRpb25NYW5hZ2UsIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZSwgZXhwb3J0UmVnaXN0cmF0aW9uTWFuYWdlIH0gZnJvbSAiQC9hcGkvbWluaWFwcC94aXFpbmcvcmVnaXN0cmF0aW9uLW1hbmFnZSI7DQppbXBvcnQgeyBnZXRBY3Rpdml0eUNvbmZpZyB9IGZyb20gIkAvYXBpL21pbmlhcHAveGlxaW5nL2FjdGl2aXR5LWNvbmZpZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlhpcWluZ1JlZ2lzdHJhdGlvbk1hbmFnZSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDopb/pnZLph5Hnp43lrZDot6/mvJTmiqXlkI3nrqHnkIbooajmoLzmlbDmja4NCiAgICAgIHJlZ2lzdHJhdGlvbk1hbmFnZUxpc3Q6IFtdLA0KICAgICAgLy8g5piv5ZCm5pi+56S65p+l55yL5by55Ye65bGCDQogICAgICB2aWV3T3BlbjogZmFsc2UsDQoNCiAgICAgIC8vIOihqOWNleaVsOaNruWIl+ihqA0KICAgICAgZm9ybURhdGFMaXN0OiBbXSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBhY3Rpdml0eUlkOiBudWxsLA0KDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCg0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6KW/6Z2S6YeR56eN5a2Q6Lev5ryU5oql5ZCN566h55CG5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbk1hbmFnZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWQgPSByb3cucmVnaXN0cmF0aW9uSWQ7DQogICAgICBnZXRSZWdpc3RyYXRpb25NYW5hZ2UocmVnaXN0cmF0aW9uSWQpLnRoZW4oYXN5bmMgcmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBhd2FpdCB0aGlzLnBhcnNlRm9ybURhdGEoKTsNCiAgICAgICAgdGhpcy52aWV3T3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDop6PmnpDooajljZXmlbDmja4gKi8NCiAgICBhc3luYyBwYXJzZUZvcm1EYXRhKCkgew0KICAgICAgdGhpcy5mb3JtRGF0YUxpc3QgPSBbXTsNCiAgICAgIGlmICh0aGlzLmZvcm0uZm9ybURhdGEpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZSh0aGlzLmZvcm0uZm9ybURhdGEpOw0KDQogICAgICAgICAgLy8g5qOA5p+l5pWw5o2u5qC85byPDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHsNCiAgICAgICAgICAgIC8vIOaWsOagvOW8j++8muaVsOe7hOagvOW8j++8jOavj+S4quWFg+e0oOWMheWQq25hbWXjgIF0eXBl44CBbGFiZWzjgIF2YWx1ZeetieWxnuaApw0KICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUgJiYgZmllbGQudmFsdWUgIT09IHVuZGVmaW5lZCAmJiBmaWVsZC52YWx1ZSAhPT0gbnVsbCAmJiBmaWVsZC52YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3JtRGF0YUl0ZW0gPSB7DQogICAgICAgICAgICAgICAgICBrZXk6IGZpZWxkLmxhYmVsIHx8IGZpZWxkLm5hbWUsIC8vIOS8mOWFiOS9v+eUqGxhYmVs77yM5rKh5pyJ5YiZ5L2/55SobmFtZQ0KICAgICAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuZm9ybWF0RmllbGRWYWx1ZShmaWVsZC52YWx1ZSwgZmllbGQudHlwZSksDQogICAgICAgICAgICAgICAgICB0eXBlOiBmaWVsZC50eXBlDQogICAgICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgICAgIC8vIOWmguaenOaYr+aWh+S7tuexu+Wei++8jOino+aekOaWh+S7tuWIl+ihqA0KICAgICAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAnZmlsZScgJiYgZmllbGQudmFsdWUpIHsNCiAgICAgICAgICAgICAgICAgIGZvcm1EYXRhSXRlbS5maWxlTGlzdCA9IHRoaXMucGFyc2VGaWxlTGlzdChmaWVsZC52YWx1ZSk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgdGhpcy5mb3JtRGF0YUxpc3QucHVzaChmb3JtRGF0YUl0ZW0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiBkYXRhID09PSAnb2JqZWN0Jykgew0KICAgICAgICAgICAgLy8g5pen5qC85byP77ya5a+56LGh5qC85byP77yMa2V5LXZhbHVl5b2i5byPDQogICAgICAgICAgICAvLyDojrflj5bmtLvliqjnmoTooajljZXphY3nva7mnaXmmL7npLrmraPnoa7nmoTlrZfmrrXmoIfnrb4NCiAgICAgICAgICAgIGNvbnN0IGZvcm1Db25maWcgPSBhd2FpdCB0aGlzLmdldEFjdGl2aXR5Rm9ybUNvbmZpZygpOw0KICAgICAgICAgICAgY29uc3QgZmllbGRMYWJlbE1hcCA9IHt9Ow0KICAgICAgICAgICAgaWYgKGZvcm1Db25maWcpIHsNCiAgICAgICAgICAgICAgZm9ybUNvbmZpZy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgICBmaWVsZExhYmVsTWFwW2ZpZWxkLm5hbWVdID0gZmllbGQubGFiZWw7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7DQogICAgICAgICAgICAgIGlmIChkYXRhW2tleV0gIT09IHVuZGVmaW5lZCAmJiBkYXRhW2tleV0gIT09IG51bGwgJiYgZGF0YVtrZXldICE9PSAnJykgew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybURhdGFMaXN0LnB1c2goew0KICAgICAgICAgICAgICAgICAga2V5OiBmaWVsZExhYmVsTWFwW2tleV0gfHwga2V5LCAvLyDkvJjlhYjkvb/nlKjkuK3mlofmoIfnrb7vvIzmsqHmnInliJnkvb/nlKjljp/lrZfmrrXlkI0NCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBkYXRhW2tleV0sDQogICAgICAgICAgICAgICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNleaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOagvOW8j+WMluWtl+auteWAvCAqLw0KICAgIGZvcm1hdEZpZWxkVmFsdWUodmFsdWUsIHR5cGUpIHsNCiAgICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSAnJykgew0KICAgICAgICByZXR1cm4gJ+acquWhq+WGmSc7DQogICAgICB9DQoNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICdjaGVja2JveCc6DQogICAgICAgICAgLy8g5aSN6YCJ5qGG57G75Z6L77yMdmFsdWXlj6/og73mmK/mlbDnu4QNCiAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHsNCiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5sZW5ndGggPiAwID8gdmFsdWUuam9pbignLCAnKSA6ICfmnKrpgInmi6knOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGNhc2UgJ3JhZGlvJzoNCiAgICAgICAgY2FzZSAncGlja2VyJzoNCiAgICAgICAgY2FzZSAnc2VsZWN0JzoNCiAgICAgICAgICAvLyDljZXpgInnsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWUgfHwgJ+acqumAieaLqSc7DQogICAgICAgIGNhc2UgJ3RleHRhcmVhJzoNCiAgICAgICAgICAvLyDmlofmnKzln5/nsbvlnovvvIzkv53mjIHmjaLooYwNCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGNhc2UgJ2RhdGUnOg0KICAgICAgICAgIC8vIOaXpeacn+exu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZSB8fCAn5pyq6YCJ5oupJzsNCiAgICAgICAgY2FzZSAndGVsJzoNCiAgICAgICAgY2FzZSAncGhvbmUnOg0KICAgICAgICAgIC8vIOeUteivneexu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgICAgY2FzZSAnZmlsZSc6DQogICAgICAgICAgLy8g5paH5Lu257G75Z6L77yM6L+U5Zue5Y6f5aeL5YC877yM5Zyo5qih5p2/5Lit54m55q6K5aSE55CGDQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIC8vIOm7mOiupOaWh+acrOexu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDop6PmnpDmlofku7bliJfooaggKi8NCiAgICBwYXJzZUZpbGVMaXN0KGZpbGVWYWx1ZSkgew0KICAgICAgaWYgKCFmaWxlVmFsdWUpIHJldHVybiBbXTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy77yM5bCd6K+V6Kej5p6Q5Li6SlNPTg0KICAgICAgICBpZiAodHlwZW9mIGZpbGVWYWx1ZSA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICAvLyDlj6/og73mmK9KU09O5a2X56ym5LiyDQogICAgICAgICAgaWYgKGZpbGVWYWx1ZS5zdGFydHNXaXRoKCdbJykgfHwgZmlsZVZhbHVlLnN0YXJ0c1dpdGgoJ3snKSkgew0KICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShmaWxlVmFsdWUpOw0KICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkKSkgew0KICAgICAgICAgICAgICByZXR1cm4gcGFyc2VkLm1hcChmaWxlID0+ICh7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lIHx8IGZpbGUuZmlsZU5hbWUgfHwgJ+acquefpeaWh+S7ticsDQogICAgICAgICAgICAgICAgdXJsOiBmaWxlLnVybCB8fCBmaWxlLnBhdGggfHwgZmlsZQ0KICAgICAgICAgICAgICB9KSk7DQogICAgICAgICAgICB9IGVsc2UgaWYgKHBhcnNlZC51cmwgfHwgcGFyc2VkLnBhdGgpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgICAgICAgbmFtZTogcGFyc2VkLm5hbWUgfHwgcGFyc2VkLmZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnLA0KICAgICAgICAgICAgICAgIHVybDogcGFyc2VkLnVybCB8fCBwYXJzZWQucGF0aA0KICAgICAgICAgICAgICB9XTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5Y+v6IO95piv5Y2V5Liq5paH5Lu2VVJMDQogICAgICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICAgICAgbmFtZTogdGhpcy5nZXRGaWxlTmFtZUZyb21VcmwoZmlsZVZhbHVlKSwNCiAgICAgICAgICAgICAgdXJsOiBmaWxlVmFsdWUNCiAgICAgICAgICAgIH1dOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDlpoLmnpzmmK/mlbDnu4QNCiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShmaWxlVmFsdWUpKSB7DQogICAgICAgICAgcmV0dXJuIGZpbGVWYWx1ZS5tYXAoZmlsZSA9PiB7DQogICAgICAgICAgICBpZiAodHlwZW9mIGZpbGUgPT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbmFtZTogdGhpcy5nZXRGaWxlTmFtZUZyb21VcmwoZmlsZSksDQogICAgICAgICAgICAgICAgdXJsOiBmaWxlDQogICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSB8fCBmaWxlLmZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnLA0KICAgICAgICAgICAgICAgIHVybDogZmlsZS51cmwgfHwgZmlsZS5wYXRoIHx8IGZpbGUNCiAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICAvLyDlpoLmnpzmmK/lr7nosaENCiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGZpbGVWYWx1ZSA9PT0gJ29iamVjdCcpIHsNCiAgICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICAgIG5hbWU6IGZpbGVWYWx1ZS5uYW1lIHx8IGZpbGVWYWx1ZS5maWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JywNCiAgICAgICAgICAgIHVybDogZmlsZVZhbHVlLnVybCB8fCBmaWxlVmFsdWUucGF0aCB8fCBmaWxlVmFsdWUNCiAgICAgICAgICB9XTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDmlofku7bliJfooajlpLHotKU6JywgZSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBbXTsNCiAgICB9LA0KICAgIC8qKiDku45VUkzkuK3mj5Dlj5bmlofku7blkI0gKi8NCiAgICBnZXRGaWxlTmFtZUZyb21VcmwodXJsKSB7DQogICAgICBpZiAoIXVybCkgcmV0dXJuICfmnKrnn6Xmlofku7YnOw0KICAgICAgY29uc3QgcGFydHMgPSB1cmwuc3BsaXQoJy8nKTsNCiAgICAgIGNvbnN0IGZpbGVOYW1lID0gcGFydHNbcGFydHMubGVuZ3RoIC0gMV07DQogICAgICByZXR1cm4gZmlsZU5hbWUgfHwgJ+acquefpeaWh+S7tic7DQogICAgfSwNCiAgICAvKiog6I635Y+W5rS75Yqo6KGo5Y2V6YWN572uICovDQogICAgYXN5bmMgZ2V0QWN0aXZpdHlGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uYWN0aXZpdHlJZCkgew0KICAgICAgICByZXR1cm4gbnVsbDsNCiAgICAgIH0NCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QWN0aXZpdHlDb25maWcodGhpcy5mb3JtLmFjdGl2aXR5SWQpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmZvcm1Db25maWcpIHsNCiAgICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShyZXNwb25zZS5kYXRhLmZvcm1Db25maWcpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlua0u+WKqOihqOWNlemFjee9ruWksei0pTonLCBlKTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWRzID0gcm93LnJlZ2lzdHJhdGlvbklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5oql5ZCN57yW5Y+35Li6IicgKyByZWdpc3RyYXRpb25JZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxSZWdpc3RyYXRpb25NYW5hZ2UocmVnaXN0cmF0aW9uSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC94aXFpbmcvcmVnaXN0cmF0aW9uLW1hbmFnZS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGByZWdpc3RyYXRpb25fbWFuYWdlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, null]}