{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TWluaVVzZXJBZG1pbiwgZ2V0TWluaVVzZXIsIGNoYW5nZU1pbmlVc2VyU3RhdHVzLCBiYXRjaERpc2FibGVNaW5pVXNlciB9IGZyb20gIkAvYXBpL21pbmlhcHAvdXNlciI7DQppbXBvcnQgeyBnZXROb2Rlc0J5TGV2ZWwsIGdldEJhdGNoSW5kdXN0cnlJbmZvIH0gZnJvbSAiQC9hcGkvbWluaWFwcC9pbmR1c3RyeSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlVc2VyIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJywgJ3N5c191c2VyX3NleCddLA0KICBjb21wb25lbnRzOiB7DQogICAgSW1hZ2VVcGxvYWQ6ICgpID0+IGltcG9ydCgiQC9jb21wb25lbnRzL0ltYWdlVXBsb2FkIiksDQogICAgSW1hZ2VQcmV2aWV3OiAoKSA9PiBpbXBvcnQoIkAvY29tcG9uZW50cy9JbWFnZVByZXZpZXciKQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICAvLyDml6XmnJ/ojIPlm7QNCiAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICAvLyDnlKjmiLfor6bmg4Xlr7nor53moYYNCiAgICAgIG9wZW5WaWV3OiBmYWxzZSwNCiAgICAgIC8vIOeUqOaIt+ivpuaDheaVsOaNrg0KICAgICAgdmlld0Zvcm06IHt9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHNlYXJjaFZhbHVlOiB1bmRlZmluZWQsDQogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHdlaXhpbk5pY2tuYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHJlYWxOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICBncmFkdWF0aW9uWWVhcjogdW5kZWZpbmVkLA0KICAgICAgICByZWdpb246IHVuZGVmaW5lZCwNCiAgICAgICAgaW5kdXN0cnlGaWVsZDogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgLy8g5q+V5Lia5bm05Lu96YCJ6aG5DQogICAgICBncmFkdWF0aW9uWWVhcnM6IFtdLA0KICAgICAgLy8g55yB5Lu96YCJ6aG5DQogICAgICBwcm92aW5jZXM6IFsNCiAgICAgICAgJ+WMl+S6rCcsICflpKnmtKUnLCAn5rKz5YyXJywgJ+WxseilvycsICflhoXokpnlj6QnLCAn6L695a6BJywgJ+WQieaelycsICfpu5HpvpnmsZ8nLA0KICAgICAgICAn5LiK5rW3JywgJ+axn+iLjycsICfmtZnmsZ8nLCAn5a6J5b69JywgJ+emj+W7uicsICfmsZ/opb8nLCAn5bGx5LicJywgJ+ays+WNlycsDQogICAgICAgICfmuZbljJcnLCAn5rmW5Y2XJywgJ+W5v+S4nCcsICflub/opb8nLCAn5rW35Y2XJywgJ+mHjeW6hicsICflm5vlt50nLCAn6LS15beeJywNCiAgICAgICAgJ+S6keWNlycsICfopb/ol48nLCAn6ZmV6KW/JywgJ+eUmOiCgycsICfpnZLmtbcnLCAn5a6B5aSPJywgJ+aWsOeWhicsICflj7Dmub4nLA0KICAgICAgICAn6aaZ5rivJywgJ+a+s+mXqCcNCiAgICAgIF0sDQogICAgICAvLyDkuIDnuqfooYzkuJrpgInpobkNCiAgICAgIGZpcnN0TGV2ZWxJbmR1c3RyaWVzOiBbXSwNCiAgICAgIC8vIOWIl+S/oeaBrw0KICAgICAgY29sdW1uczogWw0KICAgICAgICB7IGtleTogMCwgbGFiZWw6IGDnlKjmiLfnvJblj7dgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAxLCBsYWJlbDogYOW+ruS/oeaYteensGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDIsIGxhYmVsOiBg5b6u5L+h5aS05YOPYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMywgbGFiZWw6IGDlp5PlkI1gLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA0LCBsYWJlbDogYOaJi+acuuWPt+eggWAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDUsIGxhYmVsOiBg5b2i6LGh54WnYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogNiwgbGFiZWw6IGDmr5XkuJrpmaLmoKFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA3LCBsYWJlbDogYOaJgOWxnuS8geS4mmAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDgsIGxhYmVsOiBg6KGM5Lia6aKG5Z+fYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogOSwgbGFiZWw6IGDnirbmgIFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAxMCwgbGFiZWw6IGDliJvlu7rml7bpl7RgLCB2aXNpYmxlOiB0cnVlIH0NCiAgICAgIF0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHdlaXhpbk5pY2tuYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW+ruS/oeaYteensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiAxLCBtYXg6IDMwLCBtZXNzYWdlOiAi5b6u5L+h5pi156ew6ZW/5bqm5b+F6aG75ZyoMeWIsDMw5Liq5a2X56ym5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcmVhbE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtaW46IDIsIG1heDogMzAsIG1lc3NhZ2U6ICLlp5PlkI3plb/luqblv4XpobvlnKgy5YiwMzDkuKrlrZfnrKbkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwaG9uZW51bWJlcjogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszLTldXGR7OX0kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmluaXRHcmFkdWF0aW9uWWVhcnMoKTsNCiAgICB0aGlzLmluaXRGaXJzdExldmVsSW5kdXN0cmllcygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgLy8g5ZCO5Y+w566h55CG5L2/55So5paw55qEIEFQSe+8jOafpeivouaJgOacieeUqOaIt++8iOWMheaLrOWBnOeUqOeahO+8iQ0KICAgICAgbGlzdE1pbmlVc2VyQWRtaW4odGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOeUqOaIt+eKtuaAgeS/ruaUuQ0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIC8vIOS9v+eUqOecn+WunuWnk+WQjeaIluW+ruS/oeaYteensOS9nOS4uuaYvuekuuWQjeensA0KICAgICAgbGV0IGRpc3BsYXlOYW1lID0gcm93LnJlYWxOYW1lIHx8IHJvdy53ZWl4aW5OaWNrbmFtZSB8fCByb3cudXNlck5hbWUgfHwgJ+ivpeeUqOaItyc7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgZGlzcGxheU5hbWUgKyAnIueUqOaIt+WQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VNaW5pVXNlclN0YXR1cyhyb3cudXNlcklkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbigpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKu5ZKM6KGo5Y2V6YeN572u5pa55rOV5bey56e76Zmk77ya5LiN5YaN6ZyA6KaB5L+u5pS55Yqf6IO9DQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS51c2VySWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCg0KDQogICAgLyoqIOafpeeci+ivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGFzeW5jIGhhbmRsZVZpZXcocm93KSB7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkOw0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNaW5pVXNlcih1c2VySWQpOw0KICAgICAgICB0aGlzLnZpZXdGb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g6Kej5p6Q6KGM5Lia5qCH562+DQogICAgICAgIGF3YWl0IHRoaXMucGFyc2VJbmR1c3RyeVRhZ3ModGhpcy52aWV3Rm9ybSk7DQogICAgICAgIHRoaXMub3BlblZpZXcgPSB0cnVlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi36K+m5oOF5aSx6LSlJywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6I635Y+W55So5oi36K+m5oOF5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6Kej5p6Q6KGM5Lia5qCH562+ICovDQogICAgYXN5bmMgcGFyc2VJbmR1c3RyeVRhZ3ModXNlcikgew0KICAgICAgaWYgKCF1c2VyLmluZHVzdHJ5RmllbGQpIHsNCiAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBpbmR1c3RyeUlkcyA9IHVzZXIuaW5kdXN0cnlGaWVsZC5zcGxpdCgnLCcpDQogICAgICAgICAgLmZpbHRlcihpZCA9PiBpZC50cmltKCkpDQogICAgICAgICAgLm1hcChpZCA9PiBwYXJzZUludChpZC50cmltKCkpKQ0KICAgICAgICAgIC5maWx0ZXIoaWQgPT4gIWlzTmFOKGlkKSk7DQoNCiAgICAgICAgaWYgKGluZHVzdHJ5SWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHVzZXIuaW5kdXN0cnlUYWdzID0gW107DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5om56YeP5p+l6K+i6KGM5Lia5L+h5oGvDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QmF0Y2hJbmR1c3RyeUluZm8oaW5kdXN0cnlJZHMpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgICBpZDogaXRlbS5pZCwNCiAgICAgICAgICAgIG5vZGVOYW1lOiBpdGVtLm5vZGVOYW1lLA0KICAgICAgICAgICAgbm9kZVR5cGU6IGl0ZW0ubm9kZVR5cGUsDQogICAgICAgICAgICBub2RlTGV2ZWw6IGl0ZW0ubm9kZUxldmVsLA0KICAgICAgICAgICAgc3RyZWFtVHlwZTogaXRlbS5zdHJlYW1UeXBlLA0KICAgICAgICAgICAgcm9vdE5vZGU6IGl0ZW0ucm9vdE5vZGUNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdXNlci5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGM5Lia5qCH562+5aSx6LSlJywgZXJyb3IpOw0KICAgICAgICB1c2VyLmluZHVzdHJ5VGFncyA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOino+aekOe8lui+keihqOWNleS4reeahOihjOS4muagh+etviAtIOW3suemgeeUqCAqLw0KICAgIC8qDQogICAgYXN5bmMgcGFyc2VGb3JtSW5kdXN0cnlUYWdzKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uaW5kdXN0cnlGaWVsZCkgew0KICAgICAgICB0aGlzLmZvcm0uaW5kdXN0cnlUYWdzID0gW107DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgaW5kdXN0cnlJZHMgPSB0aGlzLmZvcm0uaW5kdXN0cnlGaWVsZC5zcGxpdCgnLCcpLmZpbHRlcihpZCA9PiBpZC50cmltKCkpOw0KICAgICAgICBjb25zdCBpbmR1c3RyeVRhZ3MgPSBbXTsNCg0KICAgICAgICBmb3IgKGNvbnN0IGluZHVzdHJ5SWQgb2YgaW5kdXN0cnlJZHMpIHsNCiAgICAgICAgICBpZiAoaW5kdXN0cnlJZC50cmltKCkpIHsNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0SW5kdXN0cnlOb2RlSW5mbyhpbmR1c3RyeUlkLnRyaW0oKSk7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgICAgICAgaW5kdXN0cnlUYWdzLnB1c2goew0KICAgICAgICAgICAgICAgICAgaWQ6IHJlc3BvbnNlLmRhdGEuaWQsDQogICAgICAgICAgICAgICAgICBub2RlTmFtZTogcmVzcG9uc2UuZGF0YS5ub2RlTmFtZSwNCiAgICAgICAgICAgICAgICAgIG5vZGVUeXBlOiByZXNwb25zZS5kYXRhLm5vZGVUeXBlLA0KICAgICAgICAgICAgICAgICAgbm9kZUxldmVsOiByZXNwb25zZS5kYXRhLm5vZGVMZXZlbA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYOiOt+WPluihjOS4muS/oeaBr+Wksei0pe+8jElEOiAke2luZHVzdHJ5SWR9YCwgZXJyb3IpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuZm9ybS5pbmR1c3RyeVRhZ3MgPSBpbmR1c3RyeVRhZ3M7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDnvJbovpHooajljZXooYzkuJrmoIfnrb7lpLHotKUnLCBlcnJvcik7DQogICAgICAgIHRoaXMuZm9ybS5pbmR1c3RyeVRhZ3MgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgICovDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAtIOW3suemgeeUqCAqLw0KICAgIC8qDQogICAgYXN5bmMgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TWluaVVzZXIodXNlcklkKTsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8g6Kej5p6Q6KGM5Lia5qCH562+DQogICAgICAgIGF3YWl0IHRoaXMucGFyc2VGb3JtSW5kdXN0cnlUYWdzKCk7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS555So5oi3IjsNCiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gIiI7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKUnLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgICovDQoNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuIC0g5bey56aB55SoICovDQogICAgLyoNCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0udXNlcklkICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgdXBkYXRlTWluaVVzZXIodGhpcy5mb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAqLw0KICAgIC8qKiDlgZznlKjmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEaXNhYmxlKHJvdykgew0KICAgICAgbGV0IGNvbmZpcm1NZXNzYWdlOw0KICAgICAgbGV0IHVzZXJJZHMgPSBbXTsNCg0KICAgICAgaWYgKHJvdy51c2VySWQpIHsNCiAgICAgICAgLy8g5Y2V5Liq5YGc55SoDQogICAgICAgIHVzZXJJZHMgPSBbcm93LnVzZXJJZF07DQogICAgICAgIGxldCBkaXNwbGF5TmFtZSA9IHJvdy5yZWFsTmFtZSB8fCByb3cud2VpeGluTmlja25hbWUgfHwgcm93LnVzZXJOYW1lIHx8ICfor6XnlKjmiLcnOw0KICAgICAgICBjb25maXJtTWVzc2FnZSA9ICfmmK/lkKbnoa7orqTlgZznlKjnlKjmiLciJyArIGRpc3BsYXlOYW1lICsgJyLvvJ/lgZznlKjlkI7or6XnlKjmiLflsIbml6Dms5XnmbvlvZXlsI/nqIvluo/jgIInOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5om56YeP5YGc55SoDQogICAgICAgIHVzZXJJZHMgPSB0aGlzLmlkczsNCiAgICAgICAgY29uZmlybU1lc3NhZ2UgPSAn5piv5ZCm56Gu6K6k5YGc55So6YCJ5Lit55qEJyArIHRoaXMuaWRzLmxlbmd0aCArICfkuKrnlKjmiLfvvJ/lgZznlKjlkI7ov5nkupvnlKjmiLflsIbml6Dms5XnmbvlvZXlsI/nqIvluo/jgIInOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGNvbmZpcm1NZXNzYWdlKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6LCD55So5om56YeP5YGc55SoQVBJDQogICAgICAgIHJldHVybiBiYXRjaERpc2FibGVNaW5pVXNlcih1c2VySWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5YGc55So5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC91c2VyL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHVzZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog5Yid5aeL5YyW5q+V5Lia5bm05Lu96YCJ6aG5ICovDQogICAgaW5pdEdyYWR1YXRpb25ZZWFycygpIHsNCiAgICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3Qgc3RhcnRZZWFyID0gMTk4MDsNCiAgICAgIHRoaXMuZ3JhZHVhdGlvblllYXJzID0gW107DQogICAgICBmb3IgKGxldCB5ZWFyID0gY3VycmVudFllYXI7IHllYXIgPj0gc3RhcnRZZWFyOyB5ZWFyLS0pIHsNCiAgICAgICAgdGhpcy5ncmFkdWF0aW9uWWVhcnMucHVzaCh5ZWFyLnRvU3RyaW5nKCkpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOWIneWni+WMluS4gOe6p+ihjOS4mumAiemhuSAqLw0KICAgIGFzeW5jIGluaXRGaXJzdExldmVsSW5kdXN0cmllcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0Tm9kZXNCeUxldmVsKDEpOw0KICAgICAgICB0aGlzLmZpcnN0TGV2ZWxJbmR1c3RyaWVzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS4gOe6p+ihjOS4muWksei0pScsIGVycm9yKTsNCiAgICAgICAgdGhpcy5maXJzdExldmVsSW5kdXN0cmllcyA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOmihOiniOW+ruS/oeWktOWDjyAqLw0KICAgIHByZXZpZXdXZWl4aW5BdmF0YXIoYXZhdGFyVXJsKSB7DQogICAgICBpZiAoYXZhdGFyVXJsKSB7DQogICAgICAgIC8vIOWFiOaYvuekuuWKoOi9veS4reeahOWvueivneahhg0KICAgICAgICBjb25zdCBsb2FkaW5nSHRtbCA9IGANCiAgICAgICAgICA8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBjZW50ZXI7IHBhZGRpbmc6IDIwcHg7Ij4NCiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWxvYWRpbmciIHN0eWxlPSJmb250LXNpemU6IDI0cHg7IGNvbG9yOiAjNDA5RUZGOyI+PC9pPg0KICAgICAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLXRvcDogMTBweDsgY29sb3I6ICM2NjY7Ij7lpLTlg4/liqDovb3kuK0uLi48L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgYDsNCg0KICAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgICAgIHRpdGxlOiAn5b6u5L+h5aS05YOP6aKE6KeIJywNCiAgICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogbG9hZGluZ0h0bWwsDQogICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgICAgc2hvd0NvbmZpcm1CdXR0b246IHRydWUsDQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICflhbPpl60nLA0KICAgICAgICAgIGN1c3RvbUNsYXNzOiAnYXZhdGFyLXByZXZpZXctZGlhbG9nJw0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDpooTliqDovb3lm77niYcNCiAgICAgICAgY29uc3QgaW1nID0gbmV3IEltYWdlKCk7DQogICAgICAgIGltZy5vbmxvYWQgPSAoKSA9PiB7DQogICAgICAgICAgLy8g5Zu+54mH5Yqg6L295oiQ5Yqf5ZCO5pu05paw5a+56K+d5qGG5YaF5a65DQogICAgICAgICAgY29uc3QgaW1nSHRtbCA9IGANCiAgICAgICAgICAgIDxpbWcNCiAgICAgICAgICAgICAgc3JjPSIke2F2YXRhclVybH0iDQogICAgICAgICAgICAgIGFsdD0i5b6u5L+h5aS05YOP6aKE6KeIIg0KICAgICAgICAgICAgICByZWZlcnJlcnBvbGljeT0ibm8tcmVmZXJyZXIiDQogICAgICAgICAgICAgIHN0eWxlPSJtYXgtd2lkdGg6IDEwMCU7IG1heC1oZWlnaHQ6IDQwMHB4OyBvYmplY3QtZml0OiBjb250YWluOyBkaXNwbGF5OiBibG9jazsgbWFyZ2luOiAwIGF1dG87IGJvcmRlci1yYWRpdXM6IDhweDsgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOyINCiAgICAgICAgICAgIC8+DQogICAgICAgICAgYDsNCg0KICAgICAgICAgIC8vIOabtOaWsOWvueivneahhuWGheWuuQ0KICAgICAgICAgIGNvbnN0IG1lc3NhZ2VCb3ggPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuYXZhdGFyLXByZXZpZXctZGlhbG9nIC5lbC1tZXNzYWdlLWJveF9fbWVzc2FnZScpOw0KICAgICAgICAgIGlmIChtZXNzYWdlQm94KSB7DQogICAgICAgICAgICBtZXNzYWdlQm94LmlubmVySFRNTCA9IGltZ0h0bWw7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIGltZy5vbmVycm9yID0gKCkgPT4gew0KICAgICAgICAgIC8vIOWbvueJh+WKoOi9veWksei0pQ0KICAgICAgICAgIGNvbnN0IGVycm9ySHRtbCA9IGANCiAgICAgICAgICAgIDxkaXYgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlcjsgcGFkZGluZzogMjBweDsgY29sb3I6ICNGNTZDNkM7Ij4NCiAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcGljdHVyZS1vdXRsaW5lIiBzdHlsZT0iZm9udC1zaXplOiA0OHB4OyBtYXJnaW4tYm90dG9tOiAxMHB4OyI+PC9pPg0KICAgICAgICAgICAgICA8ZGl2PuWktOWDj+WKoOi9veWksei0pTwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJmb250LXNpemU6IDEycHg7IG1hcmdpbi10b3A6IDVweDsgY29sb3I6ICM5OTk7Ij7or7fmo4Dmn6XnvZHnu5zov57mjqXmiJblm77niYfpk77mjqU8L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIGA7DQoNCiAgICAgICAgICBjb25zdCBtZXNzYWdlQm94ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmF2YXRhci1wcmV2aWV3LWRpYWxvZyAuZWwtbWVzc2FnZS1ib3hfX21lc3NhZ2UnKTsNCiAgICAgICAgICBpZiAobWVzc2FnZUJveCkgew0KICAgICAgICAgICAgbWVzc2FnZUJveC5pbm5lckhUTUwgPSBlcnJvckh0bWw7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIGltZy5zcmMgPSBhdmF0YXJVcmw7DQogICAgICAgIGltZy5yZWZlcnJlclBvbGljeSA9ICJuby1yZWZlcnJlciI7DQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, null]}