import request from '@/utils/request'

// 查询科技之星列表
export function listTechstar(query) {
  return request({
    url: '/miniapp/techstar/list',
    method: 'get',
    params: query
  })
}

// 查询科技之星详细
export function getTechstar(starId) {
  return request({
    url: '/miniapp/techstar/' + starId,
    method: 'get'
  })
}

// 新增科技之星
export function addTechstar(data) {
  return request({
    url: '/miniapp/techstar',
    method: 'post',
    data: data
  })
}

// 修改科技之星
export function updateTechstar(data) {
  return request({
    url: '/miniapp/techstar',
    method: 'put',
    data: data
  })
}

// 删除科技之星
export function delTechstar(starId) {
  return request({
    url: '/miniapp/techstar/' + starId,
    method: 'delete'
  })
}

// 获取启用的科技之星列表
export function getEnabledTechstarList() {
  return request({
    url: '/miniapp/techstar/enabled',
    method: 'get'
  })
}

// 获取推荐的科技之星列表
export function getRecommendedTechstarList() {
  return request({
    url: '/miniapp/techstar/recommended',
    method: 'get'
  })
}

// 增加科技之星浏览次数
export function incrementViewCount(starId) {
  return request({
    url: '/miniapp/techstar/incrementViewCount/' + starId,
    method: 'post'
  })
}