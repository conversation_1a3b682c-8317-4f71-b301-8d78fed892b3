package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.ProjectRegistration;

/**
 * 项目报名Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProjectRegistrationMapper 
{
    /**
     * 查询项目报名
     * 
     * @param id 项目报名主键
     * @return 项目报名
     */
    public ProjectRegistration selectProjectRegistrationById(Long id);

    /**
     * 查询项目报名列表
     *
     * @param projectRegistration 项目报名
     * @return 项目报名集合
     */
    public List<ProjectRegistration> selectProjectRegistrationList(ProjectRegistration projectRegistration);

    /**
     * 新增项目报名
     *
     * @param projectRegistration 项目报名
     * @return 结果
     */
    public int insertProjectRegistration(ProjectRegistration projectRegistration);

    /**
     * 修改项目报名
     *
     * @param projectRegistration 项目报名
     * @return 结果
     */
    public int updateProjectRegistration(ProjectRegistration projectRegistration);

    /**
     * 删除项目报名
     * 
     * @param id 项目报名主键
     * @return 结果
     */
    public int deleteProjectRegistrationById(Long id);

    /**
     * 批量删除项目报名
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectRegistrationByIds(Long[] ids);
} 