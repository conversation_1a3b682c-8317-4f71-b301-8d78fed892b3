{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=template&id=07a227ce&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1754037225576}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}