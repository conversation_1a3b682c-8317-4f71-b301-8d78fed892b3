{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\job\\index.vue?vue&type=template&id=795c0ac2", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\job\\index.vue", "mtime": 1754022341003}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}