{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1754037225576}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}