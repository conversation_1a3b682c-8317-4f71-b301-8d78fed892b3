import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询小程序用户列表（小程序端使用，只查询未停用用户）
export function listMiniUser(query) {
  return request({
    url: '/miniapp/user/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户列表（后台管理使用，查询所有用户包括停用的）
export function listMiniUserAdmin(query) {
  return request({
    url: '/miniapp/user/admin/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户详细
export function getMiniUser(userId) {
  return request({
    url: '/miniapp/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}



// 修改小程序用户
export function updateMiniUser(data) {
  return request({
    url: '/miniapp/user',
    method: 'put',
    data: data
  })
}

// 批量停用小程序用户
export function batchDisableMiniUser(userIds) {
  return request({
    url: '/miniapp/user/batchDisable',
    method: 'put',
    data: userIds
  })
}



// 用户状态修改
export function changeMiniUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/miniapp/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getMiniUserProfile() {
  return request({
    url: '/miniapp/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateMiniUserProfile(data) {
  return request({
    url: '/miniapp/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateMiniUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/miniapp/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadMiniAvatar(data) {
  return request({
    url: '/miniapp/user/profile/avatar',
    method: 'post',
    data: data
  })
}


