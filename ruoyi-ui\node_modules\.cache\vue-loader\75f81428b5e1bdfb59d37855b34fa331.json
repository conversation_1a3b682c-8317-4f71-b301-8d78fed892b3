{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\xiqing\\registration-manage\\index.vue", "mtime": 1754037225576}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlLCBnZXRSZWdpc3RyYXRpb25NYW5hZ2UsIGRlbFJlZ2lzdHJhdGlvbk1hbmFnZSwgZXhwb3J0UmVnaXN0cmF0aW9uTWFuYWdlIH0gZnJvbSAiQC9hcGkvbWluaWFwcC94aXFpbmcvcmVnaXN0cmF0aW9uLW1hbmFnZSI7DQppbXBvcnQgeyBnZXRBY3Rpdml0eUNvbmZpZyB9IGZyb20gIkAvYXBpL21pbmlhcHAveGlxaW5nL2FjdGl2aXR5LWNvbmZpZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlhpcWluZ1JlZ2lzdHJhdGlvbk1hbmFnZSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDopb/pnZLph5Hnp43lrZDot6/mvJTmiqXlkI3nrqHnkIbooajmoLzmlbDmja4NCiAgICAgIHJlZ2lzdHJhdGlvbk1hbmFnZUxpc3Q6IFtdLA0KICAgICAgLy8g5piv5ZCm5pi+56S65p+l55yL5by55Ye65bGCDQogICAgICB2aWV3T3BlbjogZmFsc2UsDQoNCiAgICAgIC8vIOihqOWNleaVsOaNruWIl+ihqA0KICAgICAgZm9ybURhdGFMaXN0OiBbXSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBhY3Rpdml0eUlkOiBudWxsLA0KDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCg0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6KW/6Z2S6YeR56eN5a2Q6Lev5ryU5oql5ZCN566h55CG5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0UmVnaXN0cmF0aW9uTWFuYWdlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbk1hbmFnZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWQgPSByb3cucmVnaXN0cmF0aW9uSWQ7DQogICAgICBnZXRSZWdpc3RyYXRpb25NYW5hZ2UocmVnaXN0cmF0aW9uSWQpLnRoZW4oYXN5bmMgcmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBhd2FpdCB0aGlzLnBhcnNlRm9ybURhdGEoKTsNCiAgICAgICAgdGhpcy52aWV3T3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDop6PmnpDooajljZXmlbDmja4gKi8NCiAgICBhc3luYyBwYXJzZUZvcm1EYXRhKCkgew0KICAgICAgdGhpcy5mb3JtRGF0YUxpc3QgPSBbXTsNCiAgICAgIGlmICh0aGlzLmZvcm0uZm9ybURhdGEpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZSh0aGlzLmZvcm0uZm9ybURhdGEpOw0KDQogICAgICAgICAgLy8g5qOA5p+l5pWw5o2u5qC85byPDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkpIHsNCiAgICAgICAgICAgIC8vIOaWsOagvOW8j++8muaVsOe7hOagvOW8j++8jOavj+S4quWFg+e0oOWMheWQq25hbWXjgIF0eXBl44CBbGFiZWzjgIF2YWx1ZeetieWxnuaApw0KICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUgJiYgZmllbGQudmFsdWUgIT09IHVuZGVmaW5lZCAmJiBmaWVsZC52YWx1ZSAhPT0gbnVsbCAmJiBmaWVsZC52YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3JtRGF0YUl0ZW0gPSB7DQogICAgICAgICAgICAgICAgICBrZXk6IGZpZWxkLmxhYmVsIHx8IGZpZWxkLm5hbWUsIC8vIOS8mOWFiOS9v+eUqGxhYmVs77yM5rKh5pyJ5YiZ5L2/55SobmFtZQ0KICAgICAgICAgICAgICAgICAgdmFsdWU6IHRoaXMuZm9ybWF0RmllbGRWYWx1ZShmaWVsZC52YWx1ZSwgZmllbGQudHlwZSksDQogICAgICAgICAgICAgICAgICB0eXBlOiBmaWVsZC50eXBlDQogICAgICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgICAgIC8vIOWmguaenOaYr+aWh+S7tuexu+Wei++8jOino+aekOaWh+S7tuWIl+ihqA0KICAgICAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAnZmlsZScgJiYgZmllbGQudmFsdWUpIHsNCiAgICAgICAgICAgICAgICAgIGZvcm1EYXRhSXRlbS5maWxlTGlzdCA9IHRoaXMucGFyc2VGaWxlTGlzdChmaWVsZC52YWx1ZSk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgdGhpcy5mb3JtRGF0YUxpc3QucHVzaChmb3JtRGF0YUl0ZW0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiBkYXRhID09PSAnb2JqZWN0Jykgew0KICAgICAgICAgICAgLy8g5pen5qC85byP77ya5a+56LGh5qC85byP77yMa2V5LXZhbHVl5b2i5byPDQogICAgICAgICAgICAvLyDojrflj5bmtLvliqjnmoTooajljZXphY3nva7mnaXmmL7npLrmraPnoa7nmoTlrZfmrrXmoIfnrb4NCiAgICAgICAgICAgIGNvbnN0IGZvcm1Db25maWcgPSBhd2FpdCB0aGlzLmdldEFjdGl2aXR5Rm9ybUNvbmZpZygpOw0KICAgICAgICAgICAgY29uc3QgZmllbGRMYWJlbE1hcCA9IHt9Ow0KICAgICAgICAgICAgaWYgKGZvcm1Db25maWcpIHsNCiAgICAgICAgICAgICAgZm9ybUNvbmZpZy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgICAgICBmaWVsZExhYmVsTWFwW2ZpZWxkLm5hbWVdID0gZmllbGQubGFiZWw7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7DQogICAgICAgICAgICAgIGlmIChkYXRhW2tleV0gIT09IHVuZGVmaW5lZCAmJiBkYXRhW2tleV0gIT09IG51bGwgJiYgZGF0YVtrZXldICE9PSAnJykgew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybURhdGFMaXN0LnB1c2goew0KICAgICAgICAgICAgICAgICAga2V5OiBmaWVsZExhYmVsTWFwW2tleV0gfHwga2V5LCAvLyDkvJjlhYjkvb/nlKjkuK3mlofmoIfnrb7vvIzmsqHmnInliJnkvb/nlKjljp/lrZfmrrXlkI0NCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBkYXRhW2tleV0sDQogICAgICAgICAgICAgICAgICB0eXBlOiAndGV4dCcNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNleaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOagvOW8j+WMluWtl+auteWAvCAqLw0KICAgIGZvcm1hdEZpZWxkVmFsdWUodmFsdWUsIHR5cGUpIHsNCiAgICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSAnJykgew0KICAgICAgICByZXR1cm4gJ+acquWhq+WGmSc7DQogICAgICB9DQoNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICdjaGVja2JveCc6DQogICAgICAgICAgLy8g5aSN6YCJ5qGG57G75Z6L77yMdmFsdWXlj6/og73mmK/mlbDnu4QNCiAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHsNCiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5sZW5ndGggPiAwID8gdmFsdWUuam9pbignLCAnKSA6ICfmnKrpgInmi6knOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGNhc2UgJ3JhZGlvJzoNCiAgICAgICAgY2FzZSAncGlja2VyJzoNCiAgICAgICAgY2FzZSAnc2VsZWN0JzoNCiAgICAgICAgICAvLyDljZXpgInnsbvlnosNCiAgICAgICAgICByZXR1cm4gdmFsdWUgfHwgJ+acqumAieaLqSc7DQogICAgICAgIGNhc2UgJ3RleHRhcmVhJzoNCiAgICAgICAgICAvLyDmlofmnKzln5/nsbvlnovvvIzkv53mjIHmjaLooYwNCiAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgIGNhc2UgJ2RhdGUnOg0KICAgICAgICAgIC8vIOaXpeacn+exu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZSB8fCAn5pyq6YCJ5oupJzsNCiAgICAgICAgY2FzZSAndGVsJzoNCiAgICAgICAgY2FzZSAncGhvbmUnOg0KICAgICAgICAgIC8vIOeUteivneexu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgICAgY2FzZSAnZmlsZSc6DQogICAgICAgICAgLy8g5paH5Lu257G75Z6L77yM6L+U5Zue5Y6f5aeL5YC877yM5Zyo5qih5p2/5Lit54m55q6K5aSE55CGDQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIC8vIOm7mOiupOaWh+acrOexu+Weiw0KICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDop6PmnpDmlofku7bliJfooaggKi8NCiAgICBwYXJzZUZpbGVMaXN0KGZpbGVWYWx1ZSkgew0KICAgICAgaWYgKCFmaWxlVmFsdWUpIHJldHVybiBbXTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy77yM5bCd6K+V6Kej5p6Q5Li6SlNPTg0KICAgICAgICBpZiAodHlwZW9mIGZpbGVWYWx1ZSA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICAvLyDlj6/og73mmK9KU09O5a2X56ym5LiyDQogICAgICAgICAgaWYgKGZpbGVWYWx1ZS5zdGFydHNXaXRoKCdbJykgfHwgZmlsZVZhbHVlLnN0YXJ0c1dpdGgoJ3snKSkgew0KICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShmaWxlVmFsdWUpOw0KICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkKSkgew0KICAgICAgICAgICAgICByZXR1cm4gcGFyc2VkLm1hcChmaWxlID0+ICh7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lIHx8IGZpbGUuZmlsZU5hbWUgfHwgJ+acquefpeaWh+S7ticsDQogICAgICAgICAgICAgICAgdXJsOiBmaWxlLnVybCB8fCBmaWxlLnBhdGggfHwgZmlsZQ0KICAgICAgICAgICAgICB9KSk7DQogICAgICAgICAgICB9IGVsc2UgaWYgKHBhcnNlZC51cmwgfHwgcGFyc2VkLnBhdGgpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgICAgICAgbmFtZTogcGFyc2VkLm5hbWUgfHwgcGFyc2VkLmZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnLA0KICAgICAgICAgICAgICAgIHVybDogcGFyc2VkLnVybCB8fCBwYXJzZWQucGF0aA0KICAgICAgICAgICAgICB9XTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5Y+v6IO95piv5Y2V5Liq5paH5Lu2VVJMDQogICAgICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICAgICAgbmFtZTogdGhpcy5nZXRGaWxlTmFtZUZyb21VcmwoZmlsZVZhbHVlKSwNCiAgICAgICAgICAgICAgdXJsOiBmaWxlVmFsdWUNCiAgICAgICAgICAgIH1dOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDlpoLmnpzmmK/mlbDnu4QNCiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShmaWxlVmFsdWUpKSB7DQogICAgICAgICAgcmV0dXJuIGZpbGVWYWx1ZS5tYXAoZmlsZSA9PiB7DQogICAgICAgICAgICBpZiAodHlwZW9mIGZpbGUgPT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbmFtZTogdGhpcy5nZXRGaWxlTmFtZUZyb21VcmwoZmlsZSksDQogICAgICAgICAgICAgICAgdXJsOiBmaWxlDQogICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSB8fCBmaWxlLmZpbGVOYW1lIHx8ICfmnKrnn6Xmlofku7YnLA0KICAgICAgICAgICAgICAgIHVybDogZmlsZS51cmwgfHwgZmlsZS5wYXRoIHx8IGZpbGUNCiAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICAvLyDlpoLmnpzmmK/lr7nosaENCiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGZpbGVWYWx1ZSA9PT0gJ29iamVjdCcpIHsNCiAgICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICAgIG5hbWU6IGZpbGVWYWx1ZS5uYW1lIHx8IGZpbGVWYWx1ZS5maWxlTmFtZSB8fCAn5pyq55+l5paH5Lu2JywNCiAgICAgICAgICAgIHVybDogZmlsZVZhbHVlLnVybCB8fCBmaWxlVmFsdWUucGF0aCB8fCBmaWxlVmFsdWUNCiAgICAgICAgICB9XTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDmlofku7bliJfooajlpLHotKU6JywgZSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBbXTsNCiAgICB9LA0KICAgIC8qKiDku45VUkzkuK3mj5Dlj5bmlofku7blkI0gKi8NCiAgICBnZXRGaWxlTmFtZUZyb21VcmwodXJsKSB7DQogICAgICBpZiAoIXVybCkgcmV0dXJuICfmnKrnn6Xmlofku7YnOw0KICAgICAgY29uc3QgcGFydHMgPSB1cmwuc3BsaXQoJy8nKTsNCiAgICAgIGNvbnN0IGZpbGVOYW1lID0gcGFydHNbcGFydHMubGVuZ3RoIC0gMV07DQogICAgICByZXR1cm4gZmlsZU5hbWUgfHwgJ+acquefpeaWh+S7tic7DQogICAgfSwNCiAgICAvKiog6I635Y+W5rS75Yqo6KGo5Y2V6YWN572uICovDQogICAgYXN5bmMgZ2V0QWN0aXZpdHlGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uYWN0aXZpdHlJZCkgew0KICAgICAgICByZXR1cm4gbnVsbDsNCiAgICAgIH0NCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QWN0aXZpdHlDb25maWcodGhpcy5mb3JtLmFjdGl2aXR5SWQpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmZvcm1Db25maWcpIHsNCiAgICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShyZXNwb25zZS5kYXRhLmZvcm1Db25maWcpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlua0u+WKqOihqOWNlemFjee9ruWksei0pTonLCBlKTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWRzID0gcm93LnJlZ2lzdHJhdGlvbklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5oql5ZCN57yW5Y+35Li6IicgKyByZWdpc3RyYXRpb25JZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxSZWdpc3RyYXRpb25NYW5hZ2UocmVnaXN0cmF0aW9uSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbWluaWFwcC94aXFpbmcvcmVnaXN0cmF0aW9uLW1hbmFnZS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGByZWdpc3RyYXRpb25fbWFuYWdlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/xiqing/registration-manage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"活动ID\" prop=\"activityId\">\r\n        <el-input\r\n          v-model=\"queryParams.activityId\"\r\n          placeholder=\"请输入活动ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:xiqing:registration:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"registrationManageList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" width=\"80\" />\r\n      <el-table-column label=\"活动标题\" align=\"center\" prop=\"activityTitle\" :show-overflow-tooltip=\"true\" />\r\n\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:query']\"\r\n          >查看</el-button>\r\n\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:xiqing:registration:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看报名详情对话框 -->\r\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"报名ID\">{{ form.registrationId }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"活动标题\">{{ form.activityTitle }}</el-descriptions-item>\r\n\r\n        <el-descriptions-item label=\"报名时间\">{{ parseTime(form.registrationTime) }}</el-descriptions-item>\r\n\r\n      </el-descriptions>\r\n\r\n      <div style=\"margin-top: 20px;\">\r\n        <h4>报名表单数据：</h4>\r\n        <el-table :data=\"formDataList\" border style=\"margin-top: 10px;\">\r\n          <el-table-column prop=\"key\" label=\"字段名\" width=\"200\" />\r\n          <el-table-column label=\"字段值\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"scope.row.type === 'textarea'\" class=\"textarea-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <el-tag v-else-if=\"scope.row.type === 'radio' || scope.row.type === 'picker' || scope.row.type === 'select'\"\r\n                      type=\"primary\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <span v-else-if=\"scope.row.type === 'tel' || scope.row.type === 'phone'\"\r\n                    class=\"phone-number\">\r\n                {{ scope.row.value }}\r\n              </span>\r\n              <el-tag v-else-if=\"scope.row.type === 'date'\" type=\"info\" size=\"small\">\r\n                {{ scope.row.value }}\r\n              </el-tag>\r\n              <div v-else-if=\"scope.row.type === 'checkbox'\" class=\"checkbox-content\">\r\n                {{ scope.row.value }}\r\n              </div>\r\n              <div v-else-if=\"scope.row.type === 'file'\" class=\"file-content\">\r\n                <div v-if=\"scope.row.fileList && scope.row.fileList.length > 0\">\r\n                  <div v-for=\"(file, index) in scope.row.fileList\" :key=\"index\" class=\"file-item\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      :href=\"file.url\"\r\n                      target=\"_blank\"\r\n                      :download=\"file.name\"\r\n                      class=\"file-link\"\r\n                    >\r\n                      <i class=\"el-icon-document\"></i>\r\n                      {{ file.name }}\r\n                    </el-link>\r\n                  </div>\r\n                </div>\r\n                <span v-else class=\"no-file\">未上传文件</span>\r\n              </div>\r\n              <span v-else>{{ scope.row.value }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRegistrationManage, getRegistrationManage, delRegistrationManage, exportRegistrationManage } from \"@/api/miniapp/xiqing/registration-manage\";\r\nimport { getActivityConfig } from \"@/api/miniapp/xiqing/activity-config\";\r\n\r\nexport default {\r\n  name: \"XiqingRegistrationManage\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 西青金种子路演报名管理表格数据\r\n      registrationManageList: [],\r\n      // 是否显示查看弹出层\r\n      viewOpen: false,\r\n\r\n      // 表单数据列表\r\n      formDataList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        activityId: null,\r\n\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询西青金种子路演报名管理列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRegistrationManage(this.queryParams).then(response => {\r\n        this.registrationManageList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.registrationId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      const registrationId = row.registrationId;\r\n      getRegistrationManage(registrationId).then(async response => {\r\n        this.form = response.data;\r\n        await this.parseFormData();\r\n        this.viewOpen = true;\r\n      });\r\n    },\r\n    /** 解析表单数据 */\r\n    async parseFormData() {\r\n      this.formDataList = [];\r\n      if (this.form.formData) {\r\n        try {\r\n          const data = JSON.parse(this.form.formData);\r\n\r\n          // 检查数据格式\r\n          if (Array.isArray(data)) {\r\n            // 新格式：数组格式，每个元素包含name、type、label、value等属性\r\n            data.forEach(field => {\r\n              if (field.name && field.value !== undefined && field.value !== null && field.value !== '') {\r\n                const formDataItem = {\r\n                  key: field.label || field.name, // 优先使用label，没有则使用name\r\n                  value: this.formatFieldValue(field.value, field.type),\r\n                  type: field.type\r\n                };\r\n\r\n                // 如果是文件类型，解析文件列表\r\n                if (field.type === 'file' && field.value) {\r\n                  formDataItem.fileList = this.parseFileList(field.value);\r\n                }\r\n\r\n                this.formDataList.push(formDataItem);\r\n              }\r\n            });\r\n          } else if (typeof data === 'object') {\r\n            // 旧格式：对象格式，key-value形式\r\n            // 获取活动的表单配置来显示正确的字段标签\r\n            const formConfig = await this.getActivityFormConfig();\r\n            const fieldLabelMap = {};\r\n            if (formConfig) {\r\n              formConfig.forEach(field => {\r\n                fieldLabelMap[field.name] = field.label;\r\n              });\r\n            }\r\n\r\n            for (const key in data) {\r\n              if (data[key] !== undefined && data[key] !== null && data[key] !== '') {\r\n                this.formDataList.push({\r\n                  key: fieldLabelMap[key] || key, // 优先使用中文标签，没有则使用原字段名\r\n                  value: data[key],\r\n                  type: 'text'\r\n                });\r\n              }\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单数据失败:', e);\r\n        }\r\n      }\r\n    },\r\n    /** 格式化字段值 */\r\n    formatFieldValue(value, type) {\r\n      if (value === undefined || value === null || value === '') {\r\n        return '未填写';\r\n      }\r\n\r\n      switch (type) {\r\n        case 'checkbox':\r\n          // 复选框类型，value可能是数组\r\n          if (Array.isArray(value)) {\r\n            return value.length > 0 ? value.join(', ') : '未选择';\r\n          }\r\n          return value;\r\n        case 'radio':\r\n        case 'picker':\r\n        case 'select':\r\n          // 单选类型\r\n          return value || '未选择';\r\n        case 'textarea':\r\n          // 文本域类型，保持换行\r\n          return value;\r\n        case 'date':\r\n          // 日期类型\r\n          return value || '未选择';\r\n        case 'tel':\r\n        case 'phone':\r\n          // 电话类型\r\n          return value;\r\n        case 'file':\r\n          // 文件类型，返回原始值，在模板中特殊处理\r\n          return value;\r\n        default:\r\n          // 默认文本类型\r\n          return value;\r\n      }\r\n    },\r\n    /** 解析文件列表 */\r\n    parseFileList(fileValue) {\r\n      if (!fileValue) return [];\r\n\r\n      try {\r\n        // 如果是字符串，尝试解析为JSON\r\n        if (typeof fileValue === 'string') {\r\n          // 可能是JSON字符串\r\n          if (fileValue.startsWith('[') || fileValue.startsWith('{')) {\r\n            const parsed = JSON.parse(fileValue);\r\n            if (Array.isArray(parsed)) {\r\n              return parsed.map(file => ({\r\n                name: file.name || file.fileName || '未知文件',\r\n                url: file.url || file.path || file\r\n              }));\r\n            } else if (parsed.url || parsed.path) {\r\n              return [{\r\n                name: parsed.name || parsed.fileName || '未知文件',\r\n                url: parsed.url || parsed.path\r\n              }];\r\n            }\r\n          } else {\r\n            // 可能是单个文件URL\r\n            return [{\r\n              name: this.getFileNameFromUrl(fileValue),\r\n              url: fileValue\r\n            }];\r\n          }\r\n        }\r\n        // 如果是数组\r\n        else if (Array.isArray(fileValue)) {\r\n          return fileValue.map(file => {\r\n            if (typeof file === 'string') {\r\n              return {\r\n                name: this.getFileNameFromUrl(file),\r\n                url: file\r\n              };\r\n            } else {\r\n              return {\r\n                name: file.name || file.fileName || '未知文件',\r\n                url: file.url || file.path || file\r\n              };\r\n            }\r\n          });\r\n        }\r\n        // 如果是对象\r\n        else if (typeof fileValue === 'object') {\r\n          return [{\r\n            name: fileValue.name || fileValue.fileName || '未知文件',\r\n            url: fileValue.url || fileValue.path || fileValue\r\n          }];\r\n        }\r\n      } catch (e) {\r\n        console.error('解析文件列表失败:', e);\r\n      }\r\n\r\n      return [];\r\n    },\r\n    /** 从URL中提取文件名 */\r\n    getFileNameFromUrl(url) {\r\n      if (!url) return '未知文件';\r\n      const parts = url.split('/');\r\n      const fileName = parts[parts.length - 1];\r\n      return fileName || '未知文件';\r\n    },\r\n    /** 获取活动表单配置 */\r\n    async getActivityFormConfig() {\r\n      if (!this.form.activityId) {\r\n        return null;\r\n      }\r\n      try {\r\n        const response = await getActivityConfig(this.form.activityId);\r\n        if (response.data && response.data.formConfig) {\r\n          return JSON.parse(response.data.formConfig);\r\n        }\r\n      } catch (e) {\r\n        console.error('获取活动表单配置失败:', e);\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const registrationIds = row.registrationId || this.ids;\r\n      this.$modal.confirm('是否确认删除报名编号为\"' + registrationIds + '\"的数据项？').then(function() {\r\n        return delRegistrationManage(registrationIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/xiqing/registration-manage/export', {\r\n        ...this.queryParams\r\n      }, `registration_manage_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.textarea-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 8px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.phone-number {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.checkbox-content {\r\n  color: #606266;\r\n  line-height: 1.5;\r\n}\r\n\r\n.file-content {\r\n  max-width: 100%;\r\n}\r\n\r\n.file-item {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.file-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.file-link {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: #f0f9ff;\r\n  border: 1px solid #e1f5fe;\r\n  transition: all 0.3s;\r\n  max-width: 100%;\r\n  word-break: break-all;\r\n}\r\n\r\n.file-link:hover {\r\n  background-color: #e3f2fd;\r\n  border-color: #90caf9;\r\n}\r\n\r\n.file-link i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.no-file {\r\n  color: #909399;\r\n  font-style: italic;\r\n}\r\n</style>\r\n"]}]}