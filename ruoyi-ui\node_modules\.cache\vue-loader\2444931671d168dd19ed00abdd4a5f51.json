{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=template&id=35a4206d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}