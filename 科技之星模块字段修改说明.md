# 科技之星模块字段修改说明

## 修改背景

通过数据库分析发现，现有的前后端代码与数据库表 `mini_tech_star` 的字段结构不匹配，导致数据无法正确映射。

## 数据库表结构

`mini_tech_star` 表的主要字段：

| 数据库字段 | 类型 | 说明 |
|-----------|------|------|
| star_id | bigint | 主键ID |
| name | varchar(100) | 姓名 |
| cover_url | varchar(500) | 封面图片URL |
| description_1 | varchar(500) | 描述1（通常为公司名称） |
| description_2 | varchar(500) | 描述2（通常为职位描述） |
| top_image_url | varchar(500) | 顶部图片URL |
| middle_image_url | varchar(255) | 中间图片URL |
| middle_name | varchar(255) | 中间名称 |
| detail_introduction | longtext | 详细介绍（富文本） |
| view_count | int | 浏览次数 |
| address | varchar(255) | 地址 |
| email | varchar(255) | 邮箱 |
| sort_order | int | 排序 |
| status | char(1) | 状态 |

## 修改内容

### 1. 后端修改

#### 1.1 Entity类 (MiniTechStar.java)

**原字段 → 新字段：**
- `photoUrl` → `coverUrl` (对应 cover_url)
- `companyName` → `description1` (对应 description_1)
- `description` → `description2` (对应 description_2)
- `founderIntroduction` → `detailIntroduction` (对应 detail_introduction)
- 删除 `companyIntroduction` (数据库中不存在)

**新增字段：**
- `middleImageUrl` (对应 middle_image_url)
- `middleName` (对应 middle_name)
- `viewCount` (对应 view_count)
- `address` (对应 address)
- `email` (对应 email)

#### 1.2 Mapper.xml (MiniTechStarMapper.xml)

- 更新了 resultMap 中的字段映射关系
- 修改了 SQL 查询语句中的字段列表
- 更新了 insert 和 update 语句中的字段映射
- 修改了查询条件中的字段名

### 2. 前端修改

#### 2.1 页面表格 (index.vue)

**表格列修改：**
- "照片" → "封面图片" (prop: photoUrl → coverUrl)
- "顶图" → "顶部图片" (保持 topImageUrl)
- "公司名称" (prop: companyName → description1)
- "简介" → "职位描述" (prop: description → description2)

**新增表格列：**
- 浏览次数 (viewCount)
- 地址 (address)
- 邮箱 (email)

#### 2.2 表单字段

**表单项修改：**
- "照片地址" → "封面图片" (prop: photoUrl → coverUrl)
- "顶图地址" → "顶部图片" (保持 topImageUrl)
- "公司名称" (prop: companyName → description1)
- "简介" → "职位描述" (prop: description → description2)
- "创始人介绍" → "详细介绍" (prop: founderIntroduction → detailIntroduction)
- 删除 "公司介绍" 字段

**新增表单项：**
- 中间图片 (middleImageUrl)
- 中间名称 (middleName)
- 地址 (address)
- 邮箱 (email)

#### 2.3 查询参数和表单验证

- 查询参数：companyName → description1
- 表单验证：photoUrl → coverUrl, companyName → description1

## 数据示例

修改后的字段能正确映射数据库中的数据：

```json
{
  "starId": 1,
  "name": "小陈",
  "coverUrl": "https://tjuhaitangapi.youalltech.com/profile/upload/2025/07/30/v2-3004b399ecbe8ec00165c3f4556ed15c_r_20250730145223A044.jpg",
  "description1": "江西宇悦科技有限公司",
  "description2": "江西宇悦科技有限公司的Java高级开发",
  "topImageUrl": "https://tjuhaitangapi.youalltech.com/profile/upload/2025/07/30/v2-a5bb097f353ebc71102d5d877e04543a_1440w_20250730145227A045.jpg",
  "detailIntroduction": "<p>份地方撒地方</p>",
  "sortOrder": 0,
  "status": "0"
}
```

## 注意事项

1. 修改后需要重新编译后端项目
2. 前端页面的字段显示更加完整，包含了所有数据库字段
3. 保持了原有的业务逻辑和权限控制
4. API接口路径和方法保持不变，只是字段映射发生了变化

## 测试建议

1. 测试列表查询功能
2. 测试新增功能（确保所有字段都能正确保存）
3. 测试修改功能（确保数据能正确回显和更新）
4. 测试删除功能
5. 测试搜索功能（按姓名和公司名称搜索）
