{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue?vue&type=template&id=1a21e6f4", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\project\\index.vue", "mtime": 1754037225575}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}