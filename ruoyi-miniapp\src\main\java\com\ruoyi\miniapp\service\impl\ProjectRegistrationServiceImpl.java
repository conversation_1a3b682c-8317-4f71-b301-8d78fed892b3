package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.ProjectRegistrationMapper;
import com.ruoyi.miniapp.domain.ProjectRegistration;
import com.ruoyi.miniapp.service.IProjectRegistrationService;

/**
 * 项目报名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProjectRegistrationServiceImpl implements IProjectRegistrationService 
{
    @Autowired
    private ProjectRegistrationMapper projectRegistrationMapper;

    /**
     * 查询项目报名
     * 
     * @param id 项目报名主键
     * @return 项目报名
     */
    @Override
    public ProjectRegistration selectProjectRegistrationById(Long id)
    {
        return projectRegistrationMapper.selectProjectRegistrationById(id);
    }

    /**
     * 查询项目报名列表
     *
     * @param projectRegistration 项目报名
     * @return 项目报名
     */
    @Override
    public List<ProjectRegistration> selectProjectRegistrationList(ProjectRegistration projectRegistration)
    {
        return projectRegistrationMapper.selectProjectRegistrationList(projectRegistration);
    }

    /**
     * 新增项目报名
     *
     * @param projectRegistration 项目报名
     * @return 结果
     */
    @Override
    public int insertProjectRegistration(ProjectRegistration projectRegistration)
    {
        projectRegistration.setCreateTime(DateUtils.getNowDate());
        if (projectRegistration.getRegistrationTime() == null) {
            projectRegistration.setRegistrationTime(DateUtils.getNowDate());
        }
        return projectRegistrationMapper.insertProjectRegistration(projectRegistration);
    }

    /**
     * 修改项目报名
     *
     * @param projectRegistration 项目报名
     * @return 结果
     */
    @Override
    public int updateProjectRegistration(ProjectRegistration projectRegistration)
    {
        projectRegistration.setUpdateTime(DateUtils.getNowDate());
        return projectRegistrationMapper.updateProjectRegistration(projectRegistration);
    }

    /**
     * 批量删除项目报名
     * 
     * @param ids 需要删除的项目报名主键
     * @return 结果
     */
    @Override
    public int deleteProjectRegistrationByIds(Long[] ids)
    {
        return projectRegistrationMapper.deleteProjectRegistrationByIds(ids);
    }

    /**
     * 删除项目报名信息
     * 
     * @param id 项目报名主键
     * @return 结果
     */
    @Override
    public int deleteProjectRegistrationById(Long id)
    {
        return projectRegistrationMapper.deleteProjectRegistrationById(id);
    }

    /**
     * 根据用户ID查询项目报名列表
     *
     * @param userId 用户ID
     * @return 项目报名集合
     */
    @Override
    public List<ProjectRegistration> selectProjectRegistrationByUserId(Long userId)
    {
        ProjectRegistration projectRegistration = new ProjectRegistration();
        projectRegistration.setUserId(userId);
        return projectRegistrationMapper.selectProjectRegistrationList(projectRegistration);
    }
} 