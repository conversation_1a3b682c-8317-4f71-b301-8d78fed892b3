{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_industry", "name", "dicts", "components", "ImageUpload", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "ImagePreview", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "userList", "date<PERSON><PERSON><PERSON>", "openView", "viewForm", "queryParams", "pageNum", "pageSize", "searchValue", "undefined", "userName", "weixinNickname", "realName", "phonenumber", "status", "graduationYear", "region", "industryField", "graduationYears", "provinces", "firstLevelIndustries", "columns", "key", "label", "visible", "rules", "required", "message", "trigger", "min", "max", "pattern", "created", "getList", "initGraduationYears", "initFirstLevelIndustries", "methods", "_this", "listMiniUserAdmin", "addDateRange", "response", "rows", "handleStatusChange", "row", "_this2", "text", "displayName", "$modal", "confirm", "changeMiniUserStatus", "userId", "msgSuccess", "catch", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleView", "_this3", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getMiniUser", "v", "parseIndustryTags", "console", "error", "msgError", "a", "user", "_callee2", "industryIds", "_t2", "_context2", "industryTags", "split", "filter", "id", "trim", "parseInt", "isNaN", "getBatchIndustryInfo", "Array", "isArray", "nodeName", "nodeType", "nodeLevel", "streamType", "rootNode", "handleDisable", "_this4", "confirmMessage", "userIds", "batchDisableMiniUser", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "currentYear", "getFullYear", "startYear", "year", "push", "toString", "_this5", "_callee3", "_t3", "_context3", "getNodesByLevel", "previewWeixinAvatar", "avatarUrl", "loadingHtml", "$msgbox", "title", "dangerouslyUseHTMLString", "showCancelButton", "showConfirmButton", "confirmButtonText", "customClass", "img", "Image", "onload", "imgHtml", "messageBox", "document", "querySelector", "innerHTML", "onerror", "errorHtml", "src", "referrerPolicy"], "sources": ["src/views/miniapp/user/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!--用户数据-->\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"关键字\" prop=\"searchValue\">\r\n            <el-input\r\n              v-model=\"queryParams.searchValue\"\r\n              placeholder=\"搜索姓名、昵称、手机号等\"\r\n              clearable\r\n              style=\"width: 280px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\"\r\n              placeholder=\"用户名称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"微信昵称\" prop=\"weixinNickname\">\r\n            <el-input\r\n              v-model=\"queryParams.weixinNickname\"\r\n              placeholder=\"微信昵称\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"真实姓名\" prop=\"realName\">\r\n            <el-input\r\n              v-model=\"queryParams.realName\"\r\n              placeholder=\"真实姓名\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"手机号码\"\r\n              clearable\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"毕业年份\" prop=\"graduationYear\">\r\n            <el-select\r\n              v-model=\"queryParams.graduationYear\"\r\n              placeholder=\"请选择毕业年份\"\r\n              clearable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"year in graduationYears\"\r\n                :key=\"year\"\r\n                :label=\"year + '年'\"\r\n                :value=\"year\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地区\" prop=\"region\">\r\n            <el-select\r\n              v-model=\"queryParams.region\"\r\n              placeholder=\"请选择地区\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"province in provinces\"\r\n                :key=\"province\"\r\n                :label=\"province\"\r\n                :value=\"province\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"行业领域\" prop=\"industryField\">\r\n            <el-select\r\n              v-model=\"queryParams.industryField\"\r\n              placeholder=\"请选择行业领域\"\r\n              clearable\r\n              filterable\r\n              style=\"width: 150px\"\r\n            >\r\n              <el-option\r\n                v-for=\"industry in firstLevelIndustries\"\r\n                :key=\"industry.id\"\r\n                :label=\"industry.nodeName\"\r\n                :value=\"industry.id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 200px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n            <!-- <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >修改</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-close\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDisable\"\r\n              v-hasPermi=\"['miniapp:user:edit']\"\r\n            >停用</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['miniapp:user:export']\"\r\n            >导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n          <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" width=\"80\" />\r\n          <el-table-column label=\"微信昵称\" align=\"center\" key=\"weixinNickname\" prop=\"weixinNickname\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"微信头像\" align=\"center\" key=\"weixinAvatar\" v-if=\"columns[2].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <img v-if=\"scope.row.weixinAvatar\" :src=\"scope.row.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 40px; height: 40px; border-radius: 50%; object-fit: cover;\" />\r\n              <el-avatar v-else :size=\"40\" icon=\"el-icon-user-solid\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"姓名\" align=\"center\" key=\"realName\" prop=\"realName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n          <el-table-column label=\"形象照\" align=\"center\" key=\"portraitUrl\" v-if=\"columns[5].visible\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <image-preview :src=\"scope.row.portraitUrl\" :width=\"50\" :height=\"50\" v-if=\"scope.row.portraitUrl\"/>\r\n              <el-avatar v-else :size=\"50\" icon=\"el-icon-picture\"></el-avatar>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"毕业院校\" align=\"center\" key=\"graduateSchool\" prop=\"graduateSchool\" v-if=\"columns[6].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"所属企业\" align=\"center\" key=\"currentCompany\" prop=\"currentCompany\" v-if=\"columns[7].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"行业领域\" align=\"center\" key=\"industryField\" v-if=\"columns[8].visible\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.industryNames\">{{ scope.row.industryNames }}</span>\r\n              <span v-else style=\"color: #C0C4CC;\">未设置</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[9].visible\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[10].visible\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"180\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleView(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:query']\"\r\n              >详情</el-button>\r\n              <!-- 修改按钮已隐藏：用户信息不应由管理员修改 -->\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n              >修改</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-close\"\r\n                @click=\"handleDisable(scope.row)\"\r\n                v-hasPermi=\"['miniapp:user:edit']\"\r\n                :disabled=\"scope.row.status === '1'\"\r\n              >停用</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n    <!-- 修改用户对话框已移除：用户信息不应由管理员修改 -->\r\n\r\n\r\n    <!-- 用户详情对话框 -->\r\n    <el-dialog title=\"用户详情\" :visible.sync=\"openView\" width=\"1000px\" append-to-body>\r\n      <div class=\"user-detail-container\">\r\n        <!-- 基本信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">基本信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"用户编号\">{{ viewForm.userId }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"姓名\">{{ viewForm.realName || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"微信昵称\">{{ viewForm.weixinNickname || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"手机号码\">{{ viewForm.phonenumber || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性别\">\r\n              <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"viewForm.sex\"/>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"出生日期\">{{ parseTime(viewForm.birthDate, '{y}-{m}-{d}') || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"籍贯\">{{ viewForm.region || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"状态\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"viewForm.status\"/>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 头像信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">头像信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">微信头像</div>\r\n                <div class=\"avatar-content\">\r\n                  <img v-if=\"viewForm.weixinAvatar\" :src=\"viewForm.weixinAvatar\" alt=\"\" referrerpolicy=\"no-referrer\" style=\"width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer;\" @click=\"previewWeixinAvatar(viewForm.weixinAvatar)\" />\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"avatar-item\">\r\n                <div class=\"avatar-label\">形象照</div>\r\n                <div class=\"avatar-content\">\r\n                  <image-preview :src=\"viewForm.portraitUrl\" :width=\"80\" :height=\"80\" v-if=\"viewForm.portraitUrl\"/>\r\n                  <div v-else class=\"no-avatar\">未设置</div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 教育背景 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">教育背景</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"毕业院校\">{{ viewForm.graduateSchool || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"毕业年份\">{{ viewForm.graduationYear || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"专业\">{{ viewForm.major || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学院\">{{ viewForm.college || '未设置' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 职业信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">职业信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"当前公司\">{{ viewForm.currentCompany || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"行业领域\">\r\n              <div v-if=\"viewForm.industryTags && viewForm.industryTags.length > 0\" class=\"industry-tags\">\r\n                <el-tag\r\n                  v-for=\"tag in viewForm.industryTags\"\r\n                  :key=\"tag.id\"\r\n                  size=\"small\"\r\n                  style=\"margin-right: 5px; margin-bottom: 5px;\"\r\n                >\r\n                  {{ tag.nodeName }}\r\n                </el-tag>\r\n              </div>\r\n              <span v-else>未设置</span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"职位名称\">{{ viewForm.positionTitle || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"个人介绍\" :span=\"2\">\r\n              <div class=\"personal-intro\">\r\n                {{ viewForm.personalIntroduction || '未设置' }}\r\n              </div>\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 积分信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">积分信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item label=\"总积分\">{{ viewForm.totalPoints || 0 }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n\r\n        <!-- 系统信息 -->\r\n        <el-card class=\"detail-card\" shadow=\"never\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">系统信息</span>\r\n          </div>\r\n          <el-descriptions :column=\"2\" border>\r\n            <el-descriptions-item label=\"最后登录时间\">{{ parseTime(viewForm.lastLoginTime) || '未登录' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"创建时间\">{{ parseTime(viewForm.createTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"更新时间\">{{ parseTime(viewForm.updateTime) || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"备注\" :span=\"2\">{{ viewForm.remark || '无备注' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </el-card>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"openView = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMiniUserAdmin, getMiniUser, changeMiniUserStatus, batchDisableMiniUser } from \"@/api/miniapp/user\";\r\nimport { getNodesByLevel, getBatchIndustryInfo } from \"@/api/miniapp/industry\";\r\n\r\nexport default {\r\n  name: \"MiniUser\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: {\r\n    ImageUpload: () => import(\"@/components/ImageUpload\"),\r\n    ImagePreview: () => import(\"@/components/ImagePreview\")\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 用户详情对话框\r\n      openView: false,\r\n      // 用户详情数据\r\n      viewForm: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        searchValue: undefined,\r\n        userName: undefined,\r\n        weixinNickname: undefined,\r\n        realName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        graduationYear: undefined,\r\n        region: undefined,\r\n        industryField: undefined\r\n      },\r\n      // 毕业年份选项\r\n      graduationYears: [],\r\n      // 省份选项\r\n      provinces: [\r\n        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',\r\n        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',\r\n        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',\r\n        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾',\r\n        '香港', '澳门'\r\n      ],\r\n      // 一级行业选项\r\n      firstLevelIndustries: [],\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `微信昵称`, visible: true },\r\n        { key: 2, label: `微信头像`, visible: true },\r\n        { key: 3, label: `姓名`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `形象照`, visible: true },\r\n        { key: 6, label: `毕业院校`, visible: true },\r\n        { key: 7, label: `所属企业`, visible: true },\r\n        { key: 8, label: `行业领域`, visible: true },\r\n        { key: 9, label: `状态`, visible: true },\r\n        { key: 10, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        weixinNickname: [\r\n          { required: true, message: \"微信昵称不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 30, message: \"微信昵称长度必须在1到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        realName: [\r\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 30, message: \"姓名长度必须在2到30个字符之间\", trigger: \"blur\" }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.initGraduationYears();\r\n    this.initFirstLevelIndustries();\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 后台管理使用新的 API，查询所有用户（包括停用的）\r\n      listMiniUserAdmin(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      // 使用真实姓名或微信昵称作为显示名称\r\n      let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + displayName + '\"用户吗？').then(function() {\r\n        return changeMiniUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮和表单重置方法已移除：不再需要修改功能\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n\r\n\r\n    /** 查看详情按钮操作 */\r\n    async handleView(row) {\r\n      const userId = row.userId;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.viewForm = response.data;\r\n        // 解析行业标签\r\n        await this.parseIndustryTags(this.viewForm);\r\n        this.openView = true;\r\n      } catch (error) {\r\n        console.error('获取用户详情失败', error);\r\n        this.$modal.msgError('获取用户详情失败');\r\n      }\r\n    },\r\n    /** 解析行业标签 */\r\n    async parseIndustryTags(user) {\r\n      if (!user.industryField) {\r\n        user.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = user.industryField.split(',')\r\n          .filter(id => id.trim())\r\n          .map(id => parseInt(id.trim()))\r\n          .filter(id => !isNaN(id));\r\n\r\n        if (industryIds.length === 0) {\r\n          user.industryTags = [];\r\n          return;\r\n        }\r\n\r\n        // 批量查询行业信息\r\n        const response = await getBatchIndustryInfo(industryIds);\r\n        if (response.data && Array.isArray(response.data)) {\r\n          user.industryTags = response.data.map(item => ({\r\n            id: item.id,\r\n            nodeName: item.nodeName,\r\n            nodeType: item.nodeType,\r\n            nodeLevel: item.nodeLevel,\r\n            streamType: item.streamType,\r\n            rootNode: item.rootNode\r\n          }));\r\n        } else {\r\n          user.industryTags = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('解析行业标签失败', error);\r\n        user.industryTags = [];\r\n      }\r\n    },\r\n    /** 解析编辑表单中的行业标签 - 已禁用 */\r\n    /*\r\n    async parseFormIndustryTags() {\r\n      if (!this.form.industryField) {\r\n        this.form.industryTags = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const industryIds = this.form.industryField.split(',').filter(id => id.trim());\r\n        const industryTags = [];\r\n\r\n        for (const industryId of industryIds) {\r\n          if (industryId.trim()) {\r\n            try {\r\n              const response = await getIndustryNodeInfo(industryId.trim());\r\n              if (response.data) {\r\n                industryTags.push({\r\n                  id: response.data.id,\r\n                  nodeName: response.data.nodeName,\r\n                  nodeType: response.data.nodeType,\r\n                  nodeLevel: response.data.nodeLevel\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取行业信息失败，ID: ${industryId}`, error);\r\n            }\r\n          }\r\n        }\r\n\r\n        this.form.industryTags = industryTags;\r\n      } catch (error) {\r\n        console.error('解析编辑表单行业标签失败', error);\r\n        this.form.industryTags = [];\r\n      }\r\n    },\r\n    */\r\n    /** 修改按钮操作 - 已禁用 */\r\n    /*\r\n    async handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      try {\r\n        const response = await getMiniUser(userId);\r\n        this.form = response.data;\r\n        // 解析行业标签\r\n        await this.parseFormIndustryTags();\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      } catch (error) {\r\n        console.error('获取用户信息失败', error);\r\n        this.$modal.msgError('获取用户信息失败');\r\n      }\r\n    },\r\n    */\r\n\r\n    /** 提交按钮 - 已禁用 */\r\n    /*\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateMiniUser(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    */\r\n    /** 停用按钮操作 */\r\n    handleDisable(row) {\r\n      let confirmMessage;\r\n      let userIds = [];\r\n\r\n      if (row.userId) {\r\n        // 单个停用\r\n        userIds = [row.userId];\r\n        let displayName = row.realName || row.weixinNickname || row.userName || '该用户';\r\n        confirmMessage = '是否确认停用用户\"' + displayName + '\"？停用后该用户将无法登录小程序。';\r\n      } else {\r\n        // 批量停用\r\n        userIds = this.ids;\r\n        confirmMessage = '是否确认停用选中的' + this.ids.length + '个用户？停用后这些用户将无法登录小程序。';\r\n      }\r\n\r\n      this.$modal.confirm(confirmMessage).then(() => {\r\n        // 调用批量停用API\r\n        return batchDisableMiniUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"停用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 初始化毕业年份选项 */\r\n    initGraduationYears() {\r\n      const currentYear = new Date().getFullYear();\r\n      const startYear = 1980;\r\n      this.graduationYears = [];\r\n      for (let year = currentYear; year >= startYear; year--) {\r\n        this.graduationYears.push(year.toString());\r\n      }\r\n    },\r\n    /** 初始化一级行业选项 */\r\n    async initFirstLevelIndustries() {\r\n      try {\r\n        const response = await getNodesByLevel(1);\r\n        this.firstLevelIndustries = response.data || [];\r\n      } catch (error) {\r\n        console.error('获取一级行业失败', error);\r\n        this.firstLevelIndustries = [];\r\n      }\r\n    },\r\n    /** 预览微信头像 */\r\n    previewWeixinAvatar(avatarUrl) {\r\n      if (avatarUrl) {\r\n        // 先显示加载中的对话框\r\n        const loadingHtml = `\r\n          <div style=\"text-align: center; padding: 20px;\">\r\n            <i class=\"el-icon-loading\" style=\"font-size: 24px; color: #409EFF;\"></i>\r\n            <div style=\"margin-top: 10px; color: #666;\">头像加载中...</div>\r\n          </div>\r\n        `;\r\n\r\n        this.$msgbox({\r\n          title: '微信头像预览',\r\n          dangerouslyUseHTMLString: true,\r\n          message: loadingHtml,\r\n          showCancelButton: false,\r\n          showConfirmButton: true,\r\n          confirmButtonText: '关闭',\r\n          customClass: 'avatar-preview-dialog'\r\n        });\r\n\r\n        // 预加载图片\r\n        const img = new Image();\r\n        img.onload = () => {\r\n          // 图片加载成功后更新对话框内容\r\n          const imgHtml = `\r\n            <img\r\n              src=\"${avatarUrl}\"\r\n              alt=\"微信头像预览\"\r\n              referrerpolicy=\"no-referrer\"\r\n              style=\"max-width: 100%; max-height: 400px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\"\r\n            />\r\n          `;\r\n\r\n          // 更新对话框内容\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = imgHtml;\r\n          }\r\n        };\r\n\r\n        img.onerror = () => {\r\n          // 图片加载失败\r\n          const errorHtml = `\r\n            <div style=\"text-align: center; padding: 20px; color: #F56C6C;\">\r\n              <i class=\"el-icon-picture-outline\" style=\"font-size: 48px; margin-bottom: 10px;\"></i>\r\n              <div>头像加载失败</div>\r\n              <div style=\"font-size: 12px; margin-top: 5px; color: #999;\">请检查网络连接或图片链接</div>\r\n            </div>\r\n          `;\r\n\r\n          const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');\r\n          if (messageBox) {\r\n            messageBox.innerHTML = errorHtml;\r\n          }\r\n        };\r\n\r\n        img.src = avatarUrl;\r\n        img.referrerPolicy = \"no-referrer\";\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.user-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.detail-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0;\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.card-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 16px;\r\n  background: #409EFF;\r\n  border-radius: 2px;\r\n}\r\n\r\n.avatar-item {\r\n  text-align: center;\r\n  padding: 20px;\r\n}\r\n\r\n.avatar-label {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.avatar-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.no-avatar {\r\n  color: #C0C4CC;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 80px;\r\n  border: 2px dashed #E4E7ED;\r\n  border-radius: 50%;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-detail-container {\r\n    max-height: 60vh;\r\n  }\r\n\r\n  .avatar-item {\r\n    padding: 15px;\r\n  }\r\n\r\n  .avatar-content {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .no-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .industry-field-container .industry-tags-display {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .industry-tags .el-tag {\r\n    margin-right: 5px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .personal-intro {\r\n    max-width: 100%;\r\n    word-wrap: break-word;\r\n    white-space: pre-wrap;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyXA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAV,OAAA;MAAA;IAAA;IACAW,YAAA,WAAAA,aAAA;MAAA,OAAAL,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAV,OAAA;MAAA;IAAA;EACA;EACAY,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,cAAA,EAAAF,SAAA;QACAG,QAAA,EAAAH,SAAA;QACAI,WAAA,EAAAJ,SAAA;QACAK,MAAA,EAAAL,SAAA;QACAM,cAAA,EAAAN,SAAA;QACAO,MAAA,EAAAP,SAAA;QACAQ,aAAA,EAAAR;MACA;MACA;MACAS,eAAA;MACA;MACAC,SAAA,GACA,kDACA,gDACA,gDACA,gDACA,WACA;MACA;MACAC,oBAAA;MACA;MACAC,OAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,GACA;QAAAF,GAAA;QAAAC,KAAA;QAAAC,OAAA;MAAA,EACA;MACA;MACAC,KAAA;QACAd,cAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,QAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,WAAA,GACA;UACAkB,OAAA;UACAJ,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,mBAAA;IACA,KAAAC,wBAAA;EACA;EACAC,OAAA;IACA,aACAH,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAA1C,OAAA;MACA;MACA,IAAA2C,uBAAA,OAAAC,YAAA,MAAAlC,WAAA,OAAAH,SAAA,GAAAZ,IAAA,WAAAkD,QAAA;QACAH,KAAA,CAAApC,QAAA,GAAAuC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArC,KAAA,GAAAwC,QAAA,CAAAxC,KAAA;QACAqC,KAAA,CAAA1C,OAAA;MACA;IACA;IACA;IACA+C,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA7B,MAAA;MACA;MACA,IAAAgC,WAAA,GAAAH,GAAA,CAAA/B,QAAA,IAAA+B,GAAA,CAAAhC,cAAA,IAAAgC,GAAA,CAAAjC,QAAA;MACA,KAAAqC,MAAA,CAAAC,OAAA,UAAAH,IAAA,UAAAC,WAAA,YAAAxD,IAAA;QACA,WAAA2D,0BAAA,EAAAN,GAAA,CAAAO,MAAA,EAAAP,GAAA,CAAA7B,MAAA;MACA,GAAAxB,IAAA;QACAsD,MAAA,CAAAG,MAAA,CAAAI,UAAA,CAAAN,IAAA;MACA,GAAAO,KAAA;QACAT,GAAA,CAAA7B,MAAA,GAAA6B,GAAA,CAAA7B,MAAA;MACA;IACA;IACA;IACA;IACAuC,WAAA,WAAAA,YAAA;MACA,KAAAhD,WAAA,CAAAC,OAAA;MACA,KAAA2B,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAApD,SAAA;MACA,KAAAqD,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7D,GAAA,GAAA6D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,MAAA;MAAA;MACA,KAAArD,MAAA,GAAA4D,SAAA,CAAAG,MAAA;MACA,KAAA9D,QAAA,IAAA2D,SAAA,CAAAG,MAAA;IACA;IAGA,eACAC,UAAA,WAAAA,WAAAlB,GAAA;MAAA,IAAAmB,MAAA;MAAA,WAAAC,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAC,QAAA;QAAA,IAAAhB,MAAA,EAAAV,QAAA,EAAA2B,EAAA;QAAA,WAAAH,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACApB,MAAA,GAAAP,GAAA,CAAAO,MAAA;cAAAmB,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAE,iBAAA,EAAAtB,MAAA;YAAA;cAAAV,QAAA,GAAA6B,QAAA,CAAAI,CAAA;cACAX,MAAA,CAAA1D,QAAA,GAAAoC,QAAA,CAAA9C,IAAA;cACA;cAAA2E,QAAA,CAAAC,CAAA;cAAA,OACAR,MAAA,CAAAY,iBAAA,CAAAZ,MAAA,CAAA1D,QAAA;YAAA;cACA0D,MAAA,CAAA3D,QAAA;cAAAkE,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAAT,EAAA;cACAL,MAAA,CAAAf,MAAA,CAAA8B,QAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IACA,aACAQ,iBAAA,WAAAA,kBAAAK,IAAA;MAAA,WAAAhB,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAe,SAAA;QAAA,IAAAC,WAAA,EAAAzC,QAAA,EAAA0C,GAAA;QAAA,WAAAlB,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA;YAAA;cAAA,IACAS,IAAA,CAAA9D,aAAA;gBAAAkE,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACAS,IAAA,CAAAK,YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;YAAA;cAAAK,SAAA,CAAAZ,CAAA;cAKAU,WAAA,GAAAF,IAAA,CAAA9D,aAAA,CAAAoE,KAAA,MACAC,MAAA,WAAAC,EAAA;gBAAA,OAAAA,EAAA,CAAAC,IAAA;cAAA,GACA9B,GAAA,WAAA6B,EAAA;gBAAA,OAAAE,QAAA,CAAAF,EAAA,CAAAC,IAAA;cAAA,GACAF,MAAA,WAAAC,EAAA;gBAAA,QAAAG,KAAA,CAAAH,EAAA;cAAA;cAAA,MAEAN,WAAA,CAAArB,MAAA;gBAAAuB,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACAS,IAAA,CAAAK,YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;YAAA;cAAAK,SAAA,CAAAb,CAAA;cAAA,OAKA,IAAAqB,8BAAA,EAAAV,WAAA;YAAA;cAAAzC,QAAA,GAAA2C,SAAA,CAAAV,CAAA;cACA,IAAAjC,QAAA,CAAA9C,IAAA,IAAAkG,KAAA,CAAAC,OAAA,CAAArD,QAAA,CAAA9C,IAAA;gBACAqF,IAAA,CAAAK,YAAA,GAAA5C,QAAA,CAAA9C,IAAA,CAAAgE,GAAA,WAAAC,IAAA;kBAAA;oBACA4B,EAAA,EAAA5B,IAAA,CAAA4B,EAAA;oBACAO,QAAA,EAAAnC,IAAA,CAAAmC,QAAA;oBACAC,QAAA,EAAApC,IAAA,CAAAoC,QAAA;oBACAC,SAAA,EAAArC,IAAA,CAAAqC,SAAA;oBACAC,UAAA,EAAAtC,IAAA,CAAAsC,UAAA;oBACAC,QAAA,EAAAvC,IAAA,CAAAuC;kBACA;gBAAA;cACA;gBACAnB,IAAA,CAAAK,YAAA;cACA;cAAAD,SAAA,CAAAb,CAAA;cAAA;YAAA;cAAAa,SAAA,CAAAZ,CAAA;cAAAW,GAAA,GAAAC,SAAA,CAAAV,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAAM,GAAA;cACAH,IAAA,CAAAK,YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAL,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;IAIA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAmB,aAAA,WAAAA,cAAAxD,GAAA;MAAA,IAAAyD,MAAA;MACA,IAAAC,cAAA;MACA,IAAAC,OAAA;MAEA,IAAA3D,GAAA,CAAAO,MAAA;QACA;QACAoD,OAAA,IAAA3D,GAAA,CAAAO,MAAA;QACA,IAAAJ,WAAA,GAAAH,GAAA,CAAA/B,QAAA,IAAA+B,GAAA,CAAAhC,cAAA,IAAAgC,GAAA,CAAAjC,QAAA;QACA2F,cAAA,iBAAAvD,WAAA;MACA;QACA;QACAwD,OAAA,QAAA1G,GAAA;QACAyG,cAAA,sBAAAzG,GAAA,CAAAgE,MAAA;MACA;MAEA,KAAAb,MAAA,CAAAC,OAAA,CAAAqD,cAAA,EAAA/G,IAAA;QACA;QACA,WAAAiH,0BAAA,EAAAD,OAAA;MACA,GAAAhH,IAAA;QACA8G,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAArD,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,4BAAAC,cAAA,CAAAlH,OAAA,MACA,KAAAa,WAAA,WAAAsG,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,gBACA3E,mBAAA,WAAAA,oBAAA;MACA,IAAA4E,WAAA,OAAAF,IAAA,GAAAG,WAAA;MACA,IAAAC,SAAA;MACA,KAAA9F,eAAA;MACA,SAAA+F,IAAA,GAAAH,WAAA,EAAAG,IAAA,IAAAD,SAAA,EAAAC,IAAA;QACA,KAAA/F,eAAA,CAAAgG,IAAA,CAAAD,IAAA,CAAAE,QAAA;MACA;IACA;IACA,gBACAhF,wBAAA,WAAAA,yBAAA;MAAA,IAAAiF,MAAA;MAAA,WAAArD,kBAAA,CAAAvE,OAAA,mBAAAwE,aAAA,CAAAxE,OAAA,IAAAyE,CAAA,UAAAoD,SAAA;QAAA,IAAA7E,QAAA,EAAA8E,GAAA;QAAA,WAAAtD,aAAA,CAAAxE,OAAA,IAAA4E,CAAA,WAAAmD,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,CAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAAAgD,SAAA,CAAAjD,CAAA;cAAA,OAEA,IAAAkD,yBAAA;YAAA;cAAAhF,QAAA,GAAA+E,SAAA,CAAA9C,CAAA;cACA2C,MAAA,CAAAhG,oBAAA,GAAAoB,QAAA,CAAA9C,IAAA;cAAA6H,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAAA+C,GAAA,GAAAC,SAAA,CAAA9C,CAAA;cAEAE,OAAA,CAAAC,KAAA,aAAA0C,GAAA;cACAF,MAAA,CAAAhG,oBAAA;YAAA;cAAA,OAAAmG,SAAA,CAAAzC,CAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IAEA;IACA,aACAI,mBAAA,WAAAA,oBAAAC,SAAA;MACA,IAAAA,SAAA;QACA;QACA,IAAAC,WAAA,4RAKA;QAEA,KAAAC,OAAA;UACAC,KAAA;UACAC,wBAAA;UACAnG,OAAA,EAAAgG,WAAA;UACAI,gBAAA;UACAC,iBAAA;UACAC,iBAAA;UACAC,WAAA;QACA;;QAEA;QACA,IAAAC,GAAA,OAAAC,KAAA;QACAD,GAAA,CAAAE,MAAA;UACA;UACA,IAAAC,OAAA,8CAAA3B,MAAA,CAEAe,SAAA,+TAKA;;UAEA;UACA,IAAAa,UAAA,GAAAC,QAAA,CAAAC,aAAA;UACA,IAAAF,UAAA;YACAA,UAAA,CAAAG,SAAA,GAAAJ,OAAA;UACA;QACA;QAEAH,GAAA,CAAAQ,OAAA;UACA;UACA,IAAAC,SAAA,ybAMA;UAEA,IAAAL,UAAA,GAAAC,QAAA,CAAAC,aAAA;UACA,IAAAF,UAAA;YACAA,UAAA,CAAAG,SAAA,GAAAE,SAAA;UACA;QACA;QAEAT,GAAA,CAAAU,GAAA,GAAAnB,SAAA;QACAS,GAAA,CAAAW,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}