{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=style&index=0&id=d4eca012&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955784453}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751437913992}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5ob21lIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOw0KDQogIC8vIOasoui/juaoquW5heagt+W8jw0KICAud2VsY29tZS1iYW5uZXIgew0KICAgIGJhY2tncm91bmQ6ICM0MDllZmY7DQogICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgIHBhZGRpbmc6IDMwcHg7DQogICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICBjb2xvcjogd2hpdGU7DQogICAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4zKTsNCg0KICAgIC5iYW5uZXItdGl0bGUgew0KICAgICAgZm9udC1zaXplOiAyOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIG1hcmdpbjogMCAwIDhweCAwOw0KDQogICAgICBpIHsNCiAgICAgICAgY29sb3I6ICNmZmQ3MDA7DQogICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5iYW5uZXItc3VidGl0bGUgew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgb3BhY2l0eTogMC45Ow0KICAgICAgbWFyZ2luOiAwIDAgMjVweCAwOw0KICAgIH0NCg0KICAgIC5iYW5uZXItc3RhdHMgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGdhcDogMzBweDsNCiAgICAgIGZsZXgtd3JhcDogd3JhcDsNCg0KICAgICAgLnN0YXQtaXRlbSB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgICAgICAuc3RhdC1udW1iZXIgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgb3BhY2l0eTogMC44Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g57uf6K6h5Y2h54mH5qC35byPDQogIC5zdGF0cy1yb3cgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAuc3RhdC1jYXJkIHsNCiAgICAgIGJhY2tncm91bmQ6IHdoaXRlOw0KICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsMCwwLDAuMSk7DQogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KDQogICAgICAmOmhvdmVyIHsNCiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICAgICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwwLDAsMC4xNSk7DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWljb24gew0KICAgICAgICB3aWR0aDogNjBweDsNCiAgICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQoNCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuc3RhdC1pbmZvIHsNCiAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAuc3RhdC12YWx1ZSB7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtdGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmFjdGl2aXRpZXMgew0KICAgICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNDA5ZWZmOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LXZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogIzQwOWVmZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmRlbWFuZHMgew0KICAgICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNjdjMjNhOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LXZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogIzY3YzIzYTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAmLmV2ZW50cyB7DQogICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNlNmEyM2M7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjZTZhMjNjOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgICYuZXhwZXJ0cyB7DQogICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNmNTZjNmM7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjZjU2YzZjOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5Zu+6KGo5bGV56S65Yy65Z+f5qC35byPDQogIC5jaGFydHMtcm93IHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgLmNoYXJ0LWNvbnRhaW5lciB7DQogICAgICBwYWRkaW5nOiAxMHB4IDA7DQoNCiAgICAgIC5jaGFydCB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOS4u+imgeWGheWuueWMuuWfn+agt+W8jw0KICAubWFpbi1jb250ZW50IHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgLmNvbnRlbnQtY2FyZCB7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwwLDAsMC4xKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCg0KICAgICAgLmNhcmQtaGVhZGVyIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KDQogICAgICAgIGkgew0KICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgICAgIGNvbG9yOiAjNDA5ZWZmOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOa0u+WKqOWIl+ihqOagt+W8jw0KICAgICAgLmFjdGl2aXR5LWxpc3QsIC5kZW1hbmQtbGlzdCB7DQogICAgICAgIC5hY3Rpdml0eS1pdGVtLCAuZGVtYW5kLWl0ZW0gew0KICAgICAgICAgIHBhZGRpbmc6IDEycHggMDsNCiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5hY3Rpdml0eS10aXRsZSwgLmRlbWFuZC10aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAgICAgICAgICAgbGluZS1jbGFtcDogMjsNCiAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmFjdGl2aXR5LXRpbWUsIC5kZW1hbmQtdGltZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g57O757uf54q25oCB5qC35byPDQogICAgICAuc3lzdGVtLXN0YXR1cyB7DQogICAgICAgIC5zdGF0dXMtaXRlbSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIHBhZGRpbmc6IDE1cHggMDsNCiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCg0KICAgICAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0dXMtaWNvbiB7DQogICAgICAgICAgICB3aWR0aDogNDBweDsNCiAgICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KDQogICAgICAgICAgICBpIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzkwOTM5OTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgJi5vbmxpbmUgaSB7DQogICAgICAgICAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0dXMtaW5mbyB7DQogICAgICAgICAgICBmbGV4OiAxOw0KDQogICAgICAgICAgICAuc3RhdHVzLWxhYmVsIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4Ow0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAuc3RhdHVzLXZhbHVlIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOepuuaVsOaNruagt+W8jw0KICAgICAgLmVtcHR5LWRhdGEgew0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIHBhZGRpbmc6IDQwcHggMDsNCiAgICAgICAgY29sb3I6ICM5OTk7DQoNCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiA0OHB4Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgIH0NCg0KICAgICAgICBwIHsNCiAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5b+r5o235pON5L2c5qC35byPDQogIC5xdWljay1hY3Rpb25zIHsNCiAgICAuYWN0aW9uLWdyb3VwIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICB9DQoNCiAgICAgIGg0IHsNCiAgICAgICAgbWFyZ2luOiAwIDAgMTVweCAwOw0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDllZmY7DQogICAgICAgIHBhZGRpbmctbGVmdDogMTBweDsNCiAgICAgIH0NCg0KICAgICAgLmFjdGlvbi1idXR0b25zIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZ2FwOiAxMHB4Ow0KICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQoNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5ZON5bqU5byP6K6+6K6hDQogIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAgIHBhZGRpbmc6IDEwcHg7DQoNCiAgICAud2VsY29tZS1iYW5uZXIgew0KICAgICAgcGFkZGluZzogMjBweDsNCg0KICAgICAgLmJhbm5lci10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMjJweDsNCiAgICAgIH0NCg0KICAgICAgLmJhbm5lci1zdGF0cyB7DQogICAgICAgIGdhcDogMjBweDsNCg0KICAgICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgICAuc3RhdC1udW1iZXIgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5zdGF0cy1yb3cgew0KICAgICAgLnN0YXQtY2FyZCB7DQogICAgICAgIHBhZGRpbmc6IDE1cHg7DQoNCiAgICAgICAgLnN0YXQtaWNvbiB7DQogICAgICAgICAgd2lkdGg6IDUwcHg7DQogICAgICAgICAgaGVpZ2h0OiA1MHB4Ow0KDQogICAgICAgICAgaSB7DQogICAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtaW5mbyB7DQogICAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jaGFydHMtcm93IHsNCiAgICAgIC5jaGFydC1jb250YWluZXIgew0KICAgICAgICAuY2hhcnQgew0KICAgICAgICAgIGhlaWdodDogMjUwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAucXVpY2stYWN0aW9ucyB7DQogICAgICAuYWN0aW9uLWdyb3VwIHsNCiAgICAgICAgLmFjdGlvbi1idXR0b25zIHsNCiAgICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIHBhZGRpbmc6IDZweCAxMnB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, null]}