{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\user.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\user.js", "mtime": 1754029275400}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_ruoyi", "listMiniUser", "query", "request", "url", "method", "params", "listMiniUserAdmin", "getMiniUser", "userId", "parseStrEmpty", "updateMiniUser", "data", "batchDisableMiniUser", "userIds", "changeMiniUserStatus", "status", "getMiniUserProfile", "updateMiniUserProfile", "updateMiniUserPwd", "oldPassword", "newPassword", "uploadMiniAvatar"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport { parseStrEmpty } from \"@/utils/ruoyi\";\r\n\r\n// 查询小程序用户列表（小程序端使用，只查询未停用用户）\r\nexport function listMiniUser(query) {\r\n  return request({\r\n    url: '/miniapp/user/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询小程序用户列表（后台管理使用，查询所有用户包括停用的）\r\nexport function listMiniUserAdmin(query) {\r\n  return request({\r\n    url: '/miniapp/user/admin/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询小程序用户详细\r\nexport function getMiniUser(userId) {\r\n  return request({\r\n    url: '/miniapp/user/' + parseStrEmpty(userId),\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n\r\n\r\n// 修改小程序用户\r\nexport function updateMiniUser(data) {\r\n  return request({\r\n    url: '/miniapp/user',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 批量停用小程序用户\r\nexport function batchDisableMiniUser(userIds) {\r\n  return request({\r\n    url: '/miniapp/user/batchDisable',\r\n    method: 'put',\r\n    data: userIds\r\n  })\r\n}\r\n\r\n\r\n\r\n// 用户状态修改\r\nexport function changeMiniUserStatus(userId, status) {\r\n  const data = {\r\n    userId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/miniapp/user/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询用户个人信息\r\nexport function getMiniUserProfile() {\r\n  return request({\r\n    url: '/miniapp/user/profile',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改用户个人信息\r\nexport function updateMiniUserProfile(data) {\r\n  return request({\r\n    url: '/miniapp/user/profile',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function updateMiniUserPwd(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  return request({\r\n    url: '/miniapp/user/profile/updatePwd',\r\n    method: 'put',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 用户头像上传\r\nexport function uploadMiniAvatar(data) {\r\n  return request({\r\n    url: '/miniapp/user/profile/avatar',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACO,SAASE,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACL,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAG,IAAAM,oBAAa,EAACD,MAAM,CAAC;IAC7CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAIA;AACO,SAASM,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EAC5C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEE;EACR,CAAC,CAAC;AACJ;;AAIA;AACO,SAASC,oBAAoBA,CAACN,MAAM,EAAEO,MAAM,EAAE;EACnD,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,qBAAqBA,CAACN,IAAI,EAAE;EAC1C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,iBAAiBA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC1D,IAAMT,IAAI,GAAG;IACXQ,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEM;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,gBAAgBA,CAACV,IAAI,EAAE;EACrC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}