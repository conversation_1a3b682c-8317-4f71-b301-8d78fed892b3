{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1753955784453}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkluZGV4IiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g54mI5pys5Y+3DQogICAgICB2ZXJzaW9uOiAiMy45LjAiLA0KICAgICAgLy8g57uf6K6h5pWw5o2uDQogICAgICB0b3RhbFVzZXJzOiAwLA0KICAgICAgYWN0aXZlVXNlcnM6IDAsDQogICAgICB0b3RhbEFjdGl2aXRpZXM6IDAsDQogICAgICB0b3RhbERlbWFuZHM6IDAsDQogICAgICB0b3RhbEV2ZW50czogMCwNCiAgICAgIHRvdGFsRW50ZXJwcmlzZXM6IDAsDQogICAgICB0b3RhbEpvYnM6IDAsDQogICAgICB0b3RhbEV4cGVydHM6IDAsDQogICAgICB0b3RhbE5ld3M6IDAsDQogICAgICB0b2RheU9wZXJhdGlvbnM6IDAsDQogICAgICAvLyDmnIDmlrDmlbDmja4NCiAgICAgIHJlY2VudEFjdGl2aXRpZXM6IFtdLA0KICAgICAgcmVjZW50RGVtYW5kczogW10sDQogICAgICAvLyDlm77ooajlrp7kvosNCiAgICAgIGJ1c2luZXNzQ2hhcnQ6IG51bGwsDQogICAgICB1c2VyVHJlbmRDaGFydDogbnVsbA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmxvYWREYXNoYm9hcmREYXRhKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLmluaXRDaGFydHMoKQ0KICAgICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWDQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgfSkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LDQogICAgaWYgKHRoaXMuYnVzaW5lc3NDaGFydCkgew0KICAgICAgdGhpcy5idXNpbmVzc0NoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0NCiAgICBpZiAodGhpcy51c2VyVHJlbmRDaGFydCkgew0KICAgICAgdGhpcy51c2VyVHJlbmRDaGFydC5kaXNwb3NlKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliqDovb3ku6rooajnm5jmlbDmja4NCiAgICBhc3luYyBsb2FkRGFzaGJvYXJkRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlue7n+iuoeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRTdGF0aXN0aWNzKCkNCiAgICAgICAgLy8g6I635Y+W5pyA5paw5rS75YqoDQogICAgICAgIGF3YWl0IHRoaXMubG9hZFJlY2VudEFjdGl2aXRpZXMoKQ0KICAgICAgICAvLyDojrflj5bmnIDmlrDpnIDmsYINCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkUmVjZW50RGVtYW5kcygpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ku6rooajnm5jmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIC8vIOS9v+eUqOm7mOiupOaVsOaNrg0KICAgICAgICB0aGlzLnNldERlZmF1bHREYXRhKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L2957uf6K6h5pWw5o2uDQogICAgYXN5bmMgbG9hZFN0YXRpc3RpY3MoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKjlkI7nq69BUEnojrflj5bnu5/orqHmlbDmja4NCiAgICAgIC8vIOaaguaXtuS9v+eUqOS7juaVsOaNruW6k+afpeivouWIsOeahOecn+WunuaVsOaNrg0KICAgICAgdGhpcy50b3RhbFVzZXJzID0gNg0KICAgICAgdGhpcy5hY3RpdmVVc2VycyA9IDQgLy8g5rS76LeD55So5oi35pWw77yI5pyA6L+RN+WkqeeZu+W9leeahOeUqOaIt++8iQ0KICAgICAgdGhpcy50b3RhbEFjdGl2aXRpZXMgPSAyDQogICAgICB0aGlzLnRvdGFsRGVtYW5kcyA9IDINCiAgICAgIHRoaXMudG90YWxFdmVudHMgPSA0DQogICAgICB0aGlzLnRvdGFsRW50ZXJwcmlzZXMgPSAyDQogICAgICB0aGlzLnRvdGFsSm9icyA9IDgNCiAgICAgIHRoaXMudG90YWxFeHBlcnRzID0gNA0KICAgICAgdGhpcy50b3RhbE5ld3MgPSAyDQogICAgICB0aGlzLnRvZGF5T3BlcmF0aW9ucyA9IDM2DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veacgOaWsOa0u+WKqA0KICAgIGFzeW5jIGxvYWRSZWNlbnRBY3Rpdml0aWVzKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55So5ZCO56uvQVBJ6I635Y+W5pyA5paw5rS75YqoDQogICAgICB0aGlzLnJlY2VudEFjdGl2aXRpZXMgPSBbDQogICAgICAgIHsgaWQ6IDEsIHRpdGxlOiAi5rWL6K+V6aG1IiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTE4VDAxOjM5OjU4LjAwMFoiIH0sDQogICAgICAgIHsgaWQ6IDIsIHRpdGxlOiAi5rGf6KW/5a6H5oKm56eR5oqA5pyJ6ZmQ5YWs5Y+4IiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTE2VDAzOjE0OjI1LjAwMFoiIH0NCiAgICAgIF0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L295pyA5paw6ZyA5rGCDQogICAgYXN5bmMgbG9hZFJlY2VudERlbWFuZHMoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKjlkI7nq69BUEnojrflj5bmnIDmlrDpnIDmsYINCiAgICAgIHRoaXMucmVjZW50RGVtYW5kcyA9IFsNCiAgICAgICAgeyBpZDogMSwgZGVtYW5kX3RpdGxlOiAi5a+755CD5a+75rGC5a+75rGCIiwgY3JlYXRlX3RpbWU6ICIyMDI1LTA3LTMwVDA3OjQzOjAwLjAwMFoiIH0sDQogICAgICAgIHsgaWQ6IDIsIGRlbWFuZF90aXRsZTogIuaIkeaYr+Wkj+Wuh+adsCIsIGNyZWF0ZV90aW1lOiAiMjAyNS0wNy0zMFQwNjo1NTo0NS4wMDBaIiB9DQogICAgICBdDQogICAgfSwNCg0KICAgIC8vIOiuvue9rum7mOiupOaVsOaNrg0KICAgIHNldERlZmF1bHREYXRhKCkgew0KICAgICAgdGhpcy50b3RhbFVzZXJzID0gMA0KICAgICAgdGhpcy5hY3RpdmVVc2VycyA9IDANCiAgICAgIHRoaXMudG90YWxBY3Rpdml0aWVzID0gMA0KICAgICAgdGhpcy50b3RhbERlbWFuZHMgPSAwDQogICAgICB0aGlzLnRvdGFsRXZlbnRzID0gMA0KICAgICAgdGhpcy50b3RhbEVudGVycHJpc2VzID0gMA0KICAgICAgdGhpcy50b3RhbEpvYnMgPSAwDQogICAgICB0aGlzLnRvdGFsRXhwZXJ0cyA9IDANCiAgICAgIHRoaXMudG90YWxOZXdzID0gMA0KICAgICAgdGhpcy50b2RheU9wZXJhdGlvbnMgPSAwDQogICAgICB0aGlzLnJlY2VudEFjdGl2aXRpZXMgPSBbXQ0KICAgICAgdGhpcy5yZWNlbnREZW1hbmRzID0gW10NCiAgICB9LA0KDQogICAgLy8g5Yid5aeL5YyW5Zu+6KGoDQogICAgaW5pdENoYXJ0cygpIHsNCiAgICAgIHRoaXMuaW5pdEJ1c2luZXNzQ2hhcnQoKQ0KICAgICAgdGhpcy5pbml0VXNlclRyZW5kQ2hhcnQoKQ0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbkuJrliqHmlbDmja7liIbluIPppbzlm74NCiAgICBpbml0QnVzaW5lc3NDaGFydCgpIHsNCiAgICAgIGlmICghdGhpcy4kcmVmcy5idXNpbmVzc0NoYXJ0KSByZXR1cm4NCg0KICAgICAgdGhpcy5idXNpbmVzc0NoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuYnVzaW5lc3NDaGFydCkNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6ICfkuJrliqHmqKHlnZfliIbluIMnLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgZm9udFNpemU6IDE0LA0KICAgICAgICAgICAgY29sb3I6ICcjMzMzJw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJywNCiAgICAgICAgICBmb3JtYXR0ZXI6ICd7YX0gPGJyLz57Yn06IHtjfSAoe2R9JSknDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGJvdHRvbTogJzUlJywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+S4muWKoeaVsOaNricsDQogICAgICAgICAgICB0eXBlOiAncGllJywNCiAgICAgICAgICAgIHJhZGl1czogWyc0MCUnLCAnNzAlJ10sDQogICAgICAgICAgICBjZW50ZXI6IFsnNTAlJywgJzQ1JSddLA0KICAgICAgICAgICAgYXZvaWRMYWJlbE92ZXJsYXA6IGZhbHNlLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMTAsDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgICAgIHBvc2l0aW9uOiAnY2VudGVyJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGVtcGhhc2lzOiB7DQogICAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2JywNCiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgICAgeyB2YWx1ZTogdGhpcy50b3RhbEFjdGl2aXRpZXMsIG5hbWU6ICfnsr7lvanmtLvliqgnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNDA5ZWZmJyB9IH0sDQogICAgICAgICAgICAgIHsgdmFsdWU6IHRoaXMudG90YWxEZW1hbmRzLCBuYW1lOiAn6ZyA5rGC5a+55o6lJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzY3YzIzYScgfSB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiB0aGlzLnRvdGFsRXZlbnRzLCBuYW1lOiAn5rS75Yqo5oql5ZCNJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI2U2YTIzYycgfSB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiB0aGlzLnRvdGFsRXhwZXJ0cywgbmFtZTogJ+S4k+WutuW6kycsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNmNTZjNmMnIH0gfSwNCiAgICAgICAgICAgICAgeyB2YWx1ZTogdGhpcy50b3RhbEpvYnMsIG5hbWU6ICfmi5vogZjogYzkvY0nLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTA5Mzk5JyB9IH0sDQogICAgICAgICAgICAgIHsgdmFsdWU6IHRoaXMudG90YWxOZXdzLCBuYW1lOiAn5paw6Ze76LWE6K6vJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzYwNjI2NicgfSB9DQogICAgICAgICAgICBdDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQoNCiAgICAgIHRoaXMuYnVzaW5lc3NDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbnlKjmiLfmtLvot4Pluqbotovlir/lm74NCiAgICBpbml0VXNlclRyZW5kQ2hhcnQoKSB7DQogICAgICBpZiAoIXRoaXMuJHJlZnMudXNlclRyZW5kQ2hhcnQpIHJldHVybg0KDQogICAgICB0aGlzLnVzZXJUcmVuZENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMudXNlclRyZW5kQ2hhcnQpDQoNCiAgICAgIC8vIOaooeaLn+acgOi/kTflpKnnmoTmlbDmja4NCiAgICAgIGNvbnN0IGRhdGVzID0gW10NCiAgICAgIGNvbnN0IGFjdGl2ZURhdGEgPSBbXQ0KICAgICAgY29uc3QgdG90YWxEYXRhID0gW10NCg0KICAgICAgZm9yIChsZXQgaSA9IDY7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpDQogICAgICAgIGRhdGVzLnB1c2goZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgeyBtb250aDogJzItZGlnaXQnLCBkYXk6ICcyLWRpZ2l0JyB9KSkNCiAgICAgICAgYWN0aXZlRGF0YS5wdXNoKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUpICsgMikgLy8gMi025LmL6Ze055qE6ZqP5py65pWwDQogICAgICAgIHRvdGFsRGF0YS5wdXNoKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDMpICsgNikgLy8gNi045LmL6Ze055qE6ZqP5py65pWwDQogICAgICB9DQoNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICB0ZXh0OiAn55So5oi35rS76LeD5bqm77yI5pyA6L+RN+Wkqe+8iScsDQogICAgICAgICAgbGVmdDogJ2NlbnRlcicsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTQsDQogICAgICAgICAgICBjb2xvcjogJyMzMzMnDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnDQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGJvdHRvbTogJzUlJywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMTUlJywNCiAgICAgICAgICB0b3A6ICcyMCUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IGRhdGVzLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9udFNpemU6IDExDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTENCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmtLvot4PnlKjmiLcnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjNDA5ZWZmJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM0MDllZmYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiB7DQogICAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsDQogICAgICAgICAgICAgICAgeDogMCwNCiAgICAgICAgICAgICAgICB5OiAwLA0KICAgICAgICAgICAgICAgIHgyOiAwLA0KICAgICAgICAgICAgICAgIHkyOiAxLA0KICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7DQogICAgICAgICAgICAgICAgICBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSg2NCwgMTU4LCAyNTUsIDAuMyknDQogICAgICAgICAgICAgICAgfSwgew0KICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjEpJw0KICAgICAgICAgICAgICAgIH1dDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBhY3RpdmVEYXRhDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5oC755So5oi3JywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM2N2MyM2EnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzY3YzIzYScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiB0b3RhbERhdGENCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCg0KICAgICAgdGhpcy51c2VyVHJlbmRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnqpflj6PlpKflsI/lj5jljJYNCiAgICBoYW5kbGVSZXNpemUoKSB7DQogICAgICBpZiAodGhpcy5idXNpbmVzc0NoYXJ0KSB7DQogICAgICAgIHRoaXMuYnVzaW5lc3NDaGFydC5yZXNpemUoKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMudXNlclRyZW5kQ2hhcnQpIHsNCiAgICAgICAgdGhpcy51c2VyVHJlbmRDaGFydC5yZXNpemUoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ8NCiAgICBmb3JtYXREYXRlKGRhdGVTdHJpbmcpIHsNCiAgICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICcnDQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZykNCiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgbW9udGg6ICcyLWRpZ2l0JywNCiAgICAgICAgZGF5OiAnMi1kaWdpdCcNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluezu+e7n+i/kOihjOaXtumXtA0KICAgIGdldFVwdGltZSgpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkNCiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IG5ldyBEYXRlKCcyMDI1LTAxLTAxJykgLy8g5YGH6K6+57O757uf5ZCv5Yqo5pe26Ze0DQogICAgICBjb25zdCBkaWZmID0gbm93IC0gc3RhcnRUaW1lDQogICAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKQ0KICAgICAgcmV0dXJuIGAke2RheXN9IOWkqWANCiAgICB9LA0KDQogICAgLy8g6aG16Z2i6Lez6L2sDQogICAgZ29Ub1BhZ2UocGF0aCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2gocGF0aCkNCiAgICB9LA0KDQogICAgLy8g5aSW6YOo6ZO+5o6l6Lez6L2sDQogICAgZ29UYXJnZXQoaHJlZikgew0KICAgICAgd2luZG93Lm9wZW4oaHJlZiwgIl9ibGFuayIpDQogICAgfQ0KICB9DQp9DQo="}, null]}