{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\user\\index.vue", "mtime": 1754030832777}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}